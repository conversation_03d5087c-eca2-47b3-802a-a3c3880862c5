// Add this at the top or in a separate file for reuse
import 'package:flutter/material.dart';

import '../../theme/app_fonts.dart';

class AppButton extends StatelessWidget {
  final String label;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double borderRadius;
  final EdgeInsetsGeometry? padding;
  final double? elevation;
  final BorderSide? borderSide;
  final TextStyle? textStyle;
  final Widget? child;

  const AppButton({
    super.key,
    required this.label,
    required this.onPressed,
    this.backgroundColor,
    this.foregroundColor,
    this.borderRadius = 30,
    this.padding,
    this.elevation,
    this.borderSide,
    this.textStyle,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: foregroundColor,
        elevation: elevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
          side: borderSide ?? BorderSide.none,
        ),
        padding: padding,
      ),
      child:
          child ??
          Text(label, style: textStyle ?? AppFonts.mediumTextStyle(14)),
    );
  }
}
