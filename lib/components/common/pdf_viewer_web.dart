// pdf_viewer_web.dart
import 'package:flutter/material.dart';
import 'dart:typed_data';
import 'dart:html' as html;
import 'dart:ui_web' as ui_web;

class PdfViewer {
  static void showPdf({
    required BuildContext context,
    required Uint8List fileBytes,
    required String fileName,
  }) {
    final blob = html.Blob([fileBytes], 'application/pdf');
    final url = html.Url.createObjectUrlFromBlob(blob);
    final String viewerId = 'pdf-viewer-${DateTime.now().millisecondsSinceEpoch}';
    
    // Register the view factory using the correct import
    ui_web.platformViewRegistry.registerViewFactory(
      viewerId,
      (int viewId) {
        final iframe = html.IFrameElement()
          ..src = url
          ..style.width = '100%'
          ..style.height = '100%'
          ..style.border = 'none';
        
        return iframe;
      },
    );
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('PDF Preview - $fileName'),
        content: SizedBox(
          width: 600,
          height: 500,
          child: HtmlElementView(viewType: viewerId),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              html.Url.revokeObjectUrl(url);
            },
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              html.window.open(url, '_blank');
            },
            child: const Text('Open in New Tab'),
          ),
        ],
      ),
    );
  }
  
  static void showPdfFullScreen({
    required BuildContext context,
    required Uint8List fileBytes,
    required String fileName,
  }) {
    final blob = html.Blob([fileBytes], 'application/pdf');
    final url = html.Url.createObjectUrlFromBlob(blob);
    final String viewerId = 'pdf-viewer-fullscreen-${DateTime.now().millisecondsSinceEpoch}';
    
    // Register the view factory
    ui_web.platformViewRegistry.registerViewFactory(
      viewerId,
      (int viewId) {
        final iframe = html.IFrameElement()
          ..src = url
          ..style.width = '100%'
          ..style.height = '100%'
          ..style.border = 'none';
        
        return iframe;
      },
    );
    
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: Text(
              fileName,
              overflow: TextOverflow.ellipsis,
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.open_in_new),
                onPressed: () {
                  html.window.open(url, '_blank');
                },
                tooltip: 'Open in New Tab',
              ),
              IconButton(
                icon: const Icon(Icons.download),
                onPressed: () => _downloadPdf(fileBytes, fileName),
                tooltip: 'Download PDF',
              ),
            ],
          ),
          body: HtmlElementView(viewType: viewerId),
        ),
      ),
    ).then((_) {
      // Clean up the URL when the page is closed
      html.Url.revokeObjectUrl(url);
    });
  }
  
  static void _downloadPdf(Uint8List fileBytes, String fileName) {
    final blob = html.Blob([fileBytes], 'application/pdf');
    final url = html.Url.createObjectUrlFromBlob(blob);
    
    final anchor = html.AnchorElement(href: url)
      ..setAttribute('download', fileName)
      ..click();
    
    html.Url.revokeObjectUrl(url);
  }
}