import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'dart:async';

class PDFViewerMobile extends StatefulWidget {
  final String filePath;
  final bool enableSwipe;
  final bool swipeHorizontal;
  final bool autoSpacing;
  final bool pageFling;
  final Color backgroundColor;
  final Function(int?)? onRender;
  final Function(dynamic)? onError;
  final Function(int?, dynamic)? onPageError;
  final Function(PDFViewController)? onViewCreated;
  final Function(int?, int?)? onPageChanged;

  const PDFViewerMobile({
    super.key,
    required this.filePath,
    this.enableSwipe = true,
    this.swipeHorizontal = true,
    this.autoSpacing = false,
    this.pageFling = false,
    this.backgroundColor = Colors.grey,
    this.onRender,
    this.onError,
    this.onPageError,
    this.onViewCreated,
    this.onPageChanged,
  });

  @override
  State<PDFViewerMobile> createState() => _PDFViewerMobileState();
}

class _PDFViewerMobileState extends State<PDFViewerMobile> {
  final Completer<PDFViewController> _controller = Completer<PDFViewController>();
  int? pages = 0;
  bool isReady = false;
  String errorMessage = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PDF Viewer'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
        actions: [
          if (isReady)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Center(
                child: Text(
                  'Pages: ${pages ?? 0}',
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ),
        ],
      ),
      body: Stack(
        children: [
          if (widget.filePath.isNotEmpty)
            PDFView(
              filePath: widget.filePath,
              enableSwipe: widget.enableSwipe,
              swipeHorizontal: widget.swipeHorizontal,
              autoSpacing: widget.autoSpacing,
              pageFling: widget.pageFling,
              backgroundColor: widget.backgroundColor,
              onRender: (pagesCount) {
                setState(() {
                  pages = pagesCount;
                  isReady = true;
                });
                if (widget.onRender != null) {
                  widget.onRender!(pagesCount);
                }
              },
              onError: (error) {
                setState(() {
                  errorMessage = error.toString();
                });
                debugPrint('PDF Error: ${error.toString()}');
                if (widget.onError != null) {
                  widget.onError!(error);
                }
              },
              onPageError: (page, error) {
                debugPrint('PDF Page Error - Page $page: ${error.toString()}');
                if (widget.onPageError != null) {
                  widget.onPageError!(page, error);
                }
              },
              onViewCreated: (PDFViewController pdfViewController) {
                _controller.complete(pdfViewController);
                if (widget.onViewCreated != null) {
                  widget.onViewCreated!(pdfViewController);
                }
              },
              onPageChanged: (int? page, int? total) {
                debugPrint('PDF Page changed: $page/$total');
                if (widget.onPageChanged != null) {
                  widget.onPageChanged!(page, total);
                }
              },
            )
          else
            const Center(
              child: Text(
                'No PDF file path provided',
                style: TextStyle(fontSize: 16, color: Colors.red),
              ),
            ),
          if (!isReady && widget.filePath.isNotEmpty)
            const Center(
              child: CircularProgressIndicator(),
            ),
          if (errorMessage.isNotEmpty)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Error loading PDF',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Text(
                      errorMessage,
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        errorMessage = '';
                        isReady = false;
                      });
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}