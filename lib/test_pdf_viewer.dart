import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'services/pdf_viewer_service.dart';

class TestPDFViewer extends StatelessWidget {
  const TestPDFViewer({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PDF Viewer Test'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Platform: ${kIsWeb ? 'Web' : 'Mobile'}',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            Text(
              'PDF Viewing Supported: ${PDFViewerService.isPDFViewingSupported()}',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 40),
            ElevatedButton.icon(
              onPressed: () async {
                await PDFViewerService.openPDF(
                  context: context,
                  fileName: 'test_report.pdf',
                  fullScreen: true,
                );
              },
              icon: const Icon(Icons.picture_as_pdf),
              label: Text(kIsWeb ? 'Open PDF (Web)' : 'Open PDF (Mobile)'),
            ),
            const SizedBox(height: 20),
            if (kIsWeb)
              ElevatedButton.icon(
                onPressed: () async {
                  await PDFViewerService.downloadPDF(
                    fileName: 'test_report.pdf',
                  );
                },
                icon: const Icon(Icons.download),
                label: const Text('Download PDF'),
              ),
          ],
        ),
      ),
    );
  }
}
