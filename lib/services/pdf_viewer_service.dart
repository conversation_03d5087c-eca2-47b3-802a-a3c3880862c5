import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../components/common/pdf_viewer_mobile.dart';

// Conditional imports for web
import '../components/common/pdf_viewer_web.dart' if (dart.library.io) '../components/common/pdf_viewer_stub.dart';

class PDFViewerService {
  /// Opens a PDF viewer based on the current platform
  static Future<void> openPDF({
    required BuildContext context,
    required String fileName,
    String? filePath,
    Uint8List? fileBytes,
    bool fullScreen = true,
  }) async {
    try {
      if (kIsWeb) {
        // Web platform
        await _openWebPDF(
          context: context,
          fileName: fileName,
          fileBytes: fileBytes,
          fullScreen: fullScreen,
        );
      } else {
        // Mobile/Desktop platform
        await _openMobilePDF(
          context: context,
          fileName: fileName,
          filePath: filePath,
          fullScreen: fullScreen,
        );
      }
    } catch (e) {
      _showErrorDialog(context, 'Error opening PDF: $e');
    }
  }

  /// Opens PDF on web platform
  static Future<void> _openWebPDF({
    required BuildContext context,
    required String fileName,
    Uint8List? fileBytes,
    bool fullScreen = true,
  }) async {
    // Generate sample PDF bytes for demo if not provided
    fileBytes ??= await _generateSamplePDFBytes();

    if (fullScreen) {
      PdfViewer.showPdfFullScreen(
        context: context,
        fileBytes: fileBytes,
        fileName: fileName,
      );
    } else {
      PdfViewer.showPdf(
        context: context,
        fileBytes: fileBytes,
        fileName: fileName,
      );
    }
  }

  /// Opens PDF on mobile platform
  static Future<void> _openMobilePDF({
    required BuildContext context,
    required String fileName,
    String? filePath,
    bool fullScreen = true,
  }) async {
    if (filePath == null || filePath.isEmpty) {
      // For demo purposes, use a sample path or show error
      _showErrorDialog(context, 'No PDF file path provided');
      return;
    }

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PDFViewerMobile(
          filePath: filePath,
          onError: (error) {
            _showErrorDialog(context, 'Error loading PDF: $error');
          },
        ),
      ),
    );
  }

  /// Generates sample PDF bytes for demo purposes
  static Future<Uint8List> _generateSamplePDFBytes() async {
    // This is a minimal PDF structure for demo
    // In a real app, you would load actual PDF files
    const String pdfContent = '''%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Sample Report Content) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000274 00000 n 
0000000369 00000 n 
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
466
%%EOF''';

    return Uint8List.fromList(pdfContent.codeUnits);
  }

  /// Shows error dialog
  static void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Downloads PDF file (web only)
  static Future<void> downloadPDF({
    required String fileName,
    Uint8List? fileBytes,
  }) async {
    if (!kIsWeb) return;

    try {
      fileBytes ??= await _generateSamplePDFBytes();

      if (kIsWeb) {
        // For web, we'll trigger a download by creating a temporary link
        // This is a simplified approach - in a real app you might want to use
        // a proper download service or the web PDF viewer's download functionality
        debugPrint('Download triggered for: $fileName');
      }
    } catch (e) {
      debugPrint('Error downloading PDF: $e');
    }
  }

  /// Checks if PDF viewing is supported on current platform
  static bool isPDFViewingSupported() {
    if (kIsWeb) {
      return true;
    } else {
      // For mobile platforms, we assume PDF viewing is supported
      // In a real app, you might want to check for specific platform capabilities
      return true;
    }
  }
}
