import 'dart:ui';

class Agent {
  String name;
  int sales;
  double amount;
  double commission;
  String contact;
  String email;
  List<Agent> agents;
  Color color;
  String imageUrl;
  DateTime joinDate;
  String state;
  String city;
  String level;
  int totalDeals;
  double earning;
  bool status;
  String relatedBroker;

  Agent({
    required this.name,
    required this.sales,
    required this.amount,
    required this.commission,
    required this.contact,
    required this.email,
    required this.agents,
    required this.color,
    required this.imageUrl,
    required this.joinDate,
    required this.state,
    required this.city,
    required this.level,
    required this.totalDeals,
    required this.earning,
    required this.status,
    this.relatedBroker = '',
  });

  factory Agent.fromJson(Map<String, dynamic> json) {
    return Agent(
      name: json['name'] as String,
      sales: json['sales'] as int,
      amount: json['amount'] as double,
      commission: json['commission'] as double,
      contact: json['contact'] as String,
      email: json['email'] as String,
      agents: json['agents'] as List<Agent>,
      color: json['color'] as Color,
      imageUrl: json['imageUrl'] as String,
      joinDate: json['joinDate'] as DateTime,
      state: json['state'] as String,
      city: json['city'] as String,
      level: json['level'] as String,
      totalDeals: json['totalDeals'] as int,
      earning: json['earning'] as double,
      status: json['status'] as bool,
      relatedBroker: json['relatedBroker'] as String,
    );
  }
}
