class Sales {
  final String transactionId;
  final String agentName;
  final String propertyType;
  final String propertyAddress;
  final String propertyValue;
  final String buyerName;
  final String buyerAddress;
  final String listingDate;
  final String saleDate;
  final String salePrice;
  final String commission;
  final String commissionAmount;

  Sales({
    required this.transactionId,
    required this.agentName,
    required this.propertyType,
    required this.propertyAddress,
    required this.propertyValue,
    required this.buyerName,
    required this.buyerAddress,
    required this.listingDate,
    required this.saleDate,
    required this.salePrice,
    required this.commission,
    required this.commissionAmount,
  });

  factory Sales.fromJson(Map<String, dynamic> json) {
    return Sales(
      transactionId: json['transactionId'] ?? '',
      agentName: json['agentName'] ?? '',
      propertyType: json['propertyType'] ?? '',
      propertyAddress: json['propertyAddress'] ?? '',
      propertyValue: json['propertyValue'] ?? '',
      buyerName: json['buyerName'] ?? '',
      buyerAddress: json['buyerAddress'] ?? '',
      listingDate: json['listingDate'] ?? '',
      saleDate: json['saleDate'] ?? '',
      salePrice: json['salePrice'] ?? '',
      commission: json['commission'] ?? '',
      commissionAmount: json['commissionAmount'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'transactionId': transactionId,
      'agentName': agentName,
      'propertyType': propertyType,
      'propertyAddress': propertyAddress,
      'propertyValue': propertyValue,
      'buyerName': buyerName,
      'buyerAddress': buyerAddress,
      'listingDate': listingDate,
      'saleDate': saleDate,
      'salePrice': salePrice,
      'commission': commission,
      'commissionAmount': commissionAmount,
    };
  }
}
