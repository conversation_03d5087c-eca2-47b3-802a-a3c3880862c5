import 'package:flutter/material.dart';

class ReportModel {
  final String id;
  final String title;
  final IconData icon;
  final bool isSelected;

  ReportModel({
    required this.id,
    required this.title,
    required this.icon,
    required this.isSelected,
  });

  ReportModel copyWith({
    String? id,
    String? title,
    IconData? icon,
    bool? isSelected,
  }) {
    return ReportModel(
      id: id ?? this.id,
      title: title ?? this.title,
      icon: icon ?? this.icon,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}
