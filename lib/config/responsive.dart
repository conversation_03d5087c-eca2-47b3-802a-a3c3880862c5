import 'package:flutter/material.dart';
import '/config/constants.dart';

class Responsive extends StatelessWidget {
  final Widget? smallMobile;
  final Widget mobile;
  final Widget? tablet;
  final Widget desktop;

  const Responsive({
    Key? key,
    this.smallMobile,
    required this.mobile,
    this.tablet,
    required this.desktop,
  }) : super(key: key);

  static bool showDrawer(BuildContext context) =>
      MediaQuery.of(context).size.width < sideDrawerBreakpoint;

  static bool isMobile(BuildContext context) =>
      MediaQuery.of(context).size.width < mobileBreakpoint;

  static bool isSmallMobile(BuildContext context) {
    return MediaQuery.of(context).size.width <= 500;
  }

  static bool isTablet(BuildContext context) =>
      MediaQuery.of(context).size.width < tabletBreakpoint &&
      MediaQuery.of(context).size.width >= mobileBreakpoint;

  static bool isDesktop(BuildContext context) =>
      MediaQuery.of(context).size.width >= tabletBreakpoint;

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;

    if (size.width >= tabletBreakpoint) {
      return desktop;
    } else if (size.width >= mobileBreakpoint) {
      return tablet ?? desktop;
    } else if (size.width >= smallMobileBreakpoint) {
      return mobile;
    } else {
      return smallMobile ?? mobile;
    }
  }
}
