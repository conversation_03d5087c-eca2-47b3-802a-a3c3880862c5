import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:neorevv/config/constants.dart';
import 'package:neorevv/screens/auth/login_screen.dart';
import 'package:neorevv/screens/sales/components/sales_table.dart';
import 'package:neorevv/src/domain/repository/auth_repository.dart';
import '/screens/dashboard/dashboard_screen.dart';
import 'screens/sales/sales_review_doc_screen.dart';
import 'src/locator.dart';
import 'src/presentation/cubit/auth/cubit/auth_cubit.dart';
import 'theme/app_theme.dart';
import 'screens/broker/register_broker_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await initializeDependencies();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => AuthCubit(locator<AuthRepository>())),
      ],
      child: MaterialApp(
        debugShowCheckedModeBanner: false,
        title: 'NeoRevv Dashboard',
        theme: ThemeData(
          fontFamily: fontFamily,
          colorScheme: ColorScheme.fromSeed(
            seedColor: AppTheme.primaryColor,
            brightness: Brightness.light,
          ),

          scaffoldBackgroundColor: AppTheme.scaffoldBgColor,
        ),
        home: DashboardScreen(),
        routes: {
          '/dashboard': (context) => const DashboardScreen(),
          '/register-broker': (context) => RegisterBrokerScreen(),
          '/sale-review-doc': (context) => SalesReviewDocScreen(),
        },
      ),
    );
  }
}
