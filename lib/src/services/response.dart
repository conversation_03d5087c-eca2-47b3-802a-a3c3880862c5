import '../../enum/user_role.dart';
import '../../models/user.dart';

class LoginResponse {
  final bool success;
  final String message;
  final String? token;
  final User user;

  LoginResponse({
    required this.success,
    required this.message,
    this.token,
    required this.user,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      token: json['jwt'],
      user: json.isNotEmpty
          ? User.fromJson(json)
          : User(
              name: '',
              email: '',
              phone: '',
              image: '',
              role: UserRole.platformOwner,
            ),
    );
  }
}
