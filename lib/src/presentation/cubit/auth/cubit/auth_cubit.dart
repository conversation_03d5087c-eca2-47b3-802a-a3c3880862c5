import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:neorevv/src/domain/repository/auth_repository.dart';

import '../../../../../models/user.dart';
import '../../../../data/datasource/auth_data_source.dart';

part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  AuthCubit(this._authRepository) : super(AuthInitial());

  final AuthRepository _authRepository;

  void login(Map<String, dynamic> payload) async {
    emit(AuthLoading(true));
    try {
      final result = await AuthDataSource().login(payload);
      emit(AuthSuccess(result.user));
    } catch (e) {
      emit(AuthError(e.toString()));
    }
  }
}
