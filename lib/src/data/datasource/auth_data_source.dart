import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../services/exceptions.dart';
import '../../services/response.dart';
import '/config/api_config.dart';

class AuthDataSource {
  static const String baseUrl = APIConfig.BASE_URL;

  Future<LoginResponse> login(Map<String, dynamic> payload) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/authenticate'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          "username": "<EMAIL>",
          "password": "admin@123",
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return LoginResponse.fromJson(data);
      } else {
        final error = jsonDecode(response.body);
        throw ApiException(
          message: error['message'] ?? 'Login failed',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException(message: 'Network error: ${e.toString()}');
    }
  }
}
