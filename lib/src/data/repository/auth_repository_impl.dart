import 'dart:convert';

import 'package:neorevv/src/data/datasource/auth_data_source.dart';

import '../../../config/api_config.dart' show APIConfig;
import '../../domain/repository/auth_repository.dart';

import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../services/exceptions.dart';
import '../../services/response.dart';
import '/config/api_config.dart';

class AuthRepositoryImpl extends AuthRepository {
  AuthRepositoryImpl();

  static const String baseUrl = APIConfig.BASE_URL;

  @override
  Future<dynamic> login(Map<String, dynamic> payload) async {
    final response = await http.post(
      Uri.parse('$baseUrl/authenticate'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(payload),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return LoginResponse.fromJson(data);
    } else {
      final error = jsonDecode(response.body);
      throw ApiException(
        message: error['message'] ?? 'Login failed',
        statusCode: response.statusCode,
      );
    }
  }
}
