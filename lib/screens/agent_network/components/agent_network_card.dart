import 'package:flutter/material.dart';
import '../../../config/app_strings.dart' as AppStrings;
import '../../../config/constants.dart';
import '../../../config/responsive.dart';
import '../../../models/broker.dart';
import '../../../theme/app_theme.dart';
import '../../../theme/app_fonts.dart';
import '../../../models/agent.dart';

class AgentNetworkCard extends StatelessWidget {
  final Broker? broker;
  final Agent? agent;
  final bool isMainCard;
  final bool isBrokerCard;
  final VoidCallback? onTap;

  const AgentNetworkCard({
    super.key,
    required this.agent,
    required this.broker,
    this.isMainCard = false,
    this.isBrokerCard = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: 4,
        margin: EdgeInsets.zero,
        color: AppTheme.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(15)),
          alignment: Alignment.center,
          child: isMainCard
              ? _buildMainCardLayout(context)
              : _buildCompactCardLayout(context),
        ),
      ),
    );
  }

  Widget _buildMainCardLayout(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.roundIconColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: _buildDesktopLayout(context),
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    final isMobile = Responsive.isSmallMobile(context);
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // top section - Profile info
        _desktopMainCardProfileInfo(context),
        _buildCompleteBottomBar(context, isMobile),
      ],
    );
  }

  Widget _desktopMainCardProfileInfo(BuildContext context) {
    final isDesktop = Responsive.isDesktop(context);
    String name = isBrokerCard ? broker?.name ?? '' : agent?.name ?? '';
    String totalSalesRevenue = isBrokerCard
        ? (broker?.totalSalesRevenue ?? 0).toString()
        : (agent?.amount ?? 0).toString();
    String totalCommission = isBrokerCard
        ? (broker?.totalCommission ?? 0).toString()
        : (agent?.commission ?? 0).toString();
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding,
        vertical: defaultPadding,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          CircleAvatar(
            radius: 20,
            backgroundImage: AssetImage(broker!.imageUrl),
            backgroundColor: Colors.white.withValues(alpha: 0.2),
          ),
          const SizedBox(width: defaultPadding),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: AppFonts.semiBoldTextStyle(14, color: AppTheme.white),
                ),
                Responsive(
                  mobile: Wrap(
                    children: [
                      _mainAgentInfo(),
                      const SizedBox(height: defaultPadding * 3),
                      _mainAgentSalesInfo(
                        AppStrings.totalSalesRevenue,
                        "\$${totalSalesRevenue.toString()}",
                      ),
                      const SizedBox(width: defaultPadding),
                      _mainAgentSalesInfo(
                        AppStrings.totalCommission,
                        '\$${totalCommission}',
                      ),
                    ],
                  ),
                  tablet: Wrap(
                    children: [
                      _mainAgentInfo(),
                      const SizedBox(height: defaultPadding * 3),
                      _mainAgentSalesInfo(
                        AppStrings.totalSalesRevenue,
                        "\$${totalSalesRevenue.toString()}",
                      ),
                      const SizedBox(width: defaultPadding),
                      _mainAgentSalesInfo(
                        AppStrings.totalCommission,
                        '\$$totalCommission',
                      ),
                    ],
                  ),
                  desktop: Row(
                    children: [
                      _mainAgentInfo(),
                      Expanded(
                        child: _mainAgentSalesInfo(
                          AppStrings.totalSalesRevenue,
                          "\$${totalSalesRevenue.toString()}",
                        ),
                      ),
                      const SizedBox(width: defaultPadding),
                      Expanded(
                        child: _mainAgentSalesInfo(
                          AppStrings.totalCommission,
                          '\$$totalCommission',
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: isDesktop ? 0 : defaultPadding),
        ],
      ),
    );
  }

  Widget _mainAgentInfo() {
    String email = isBrokerCard ? broker!.email : agent!.email;
    String contact = isBrokerCard ? broker!.contact : agent!.contact;

    return Padding(
      padding: const EdgeInsets.only(right: defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 2),
          _detailsInfo('mail.png', email),
          const SizedBox(height: 3),
          _detailsInfo('phone.png', contact),
        ],
      ),
    );
  }

  Widget _detailsInfo(String icon, String value) {
    return Row(
      children: [
        Image.asset(
          '$iconAssetpath/$icon',
          height: 12,
          width: 12,
          color: AppTheme.white,
        ),
        const SizedBox(width: 4),
        _mainCardInfoText(value),
      ],
    );
  }

  Widget _mainAgentSalesInfo(String title, String value) {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _mainCardInfoText(title),
          const SizedBox(height: 4),
          Text(
            value,
            style: AppFonts.semiBoldTextStyle(14, color: AppTheme.white),
          ),
        ],
      ),
    );
  }

  Widget _mainCardInfoText(String title) {
    return Text(
      title,
      style: AppFonts.mediumTextStyle(12, color: AppTheme.white),
    );
  }

  Widget _buildCompleteBottomBar(BuildContext context, bool isMobile) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: isMobile
          ? _buildMobileBottomBar(context)
          : _buildDesktopBottomBar(context),
    );
  }

  Widget _buildMobileBottomBar(BuildContext context) {
    final isSmallMobile = Responsive.isSmallMobile(context);
    return Container(
      decoration: BoxDecoration(
        border: Border(
          right: BorderSide(color: AppTheme.borderColor, width: 1),
        ),
      ),
      padding: isSmallMobile
          ? const EdgeInsets.only(left: 12, right: 5)
          : const EdgeInsets.all(0),

      child: Row(
        children: [
          // Role Badge
          _roleWidget(
            context,
            isBrokerCard ? AppStrings.broker : AppStrings.agent,
          ),

          // View Profile Button
          _mainCardMobileProfileBtn(),
          const Spacer(),

          // recruits
          _recruitersSection(context),

          // Container(
          //   child: Row(
          //     mainAxisAlignment: MainAxisAlignment.center,
          //     children: [
          //       Icon(
          //         Icons.people_outline,
          //         size: 14,
          //         color: Colors.grey.shade600,
          //       ),
          //       const SizedBox(width: 4),
          //       Text(
          //         AppStrings.recruitsCount,
          //         style: AppFonts.regularTextStyle(
          //           13,
          //           color: AppTheme.secondaryTextColor,
          //         ),
          //       ),
          //       const SizedBox(width: 6),
          //       Container(
          //         padding: const EdgeInsets.symmetric(
          //           horizontal: 6,
          //           vertical: 2,
          //         ),
          //         decoration: BoxDecoration(
          //           color: Colors.grey.shade800,
          //           borderRadius: BorderRadius.circular(4),
          //         ),
          //         child: Text(
          //           "${agent!.agents.length}",
          //           style: AppFonts.semiBoldTextStyle(13, color: Colors.white),
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
        ],
      ),
    );
  }

  InkWell _mainCardMobileProfileBtn() {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(6),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              '$iconAssetpath/address_book.png',
              height: 14,
              width: 14,
              color: AppTheme.roundIconColor,
            ),
            const SizedBox(width: 4),
            Text(
              AppStrings.viewProfile,
              style: AppFonts.mediumTextStyle(
                13,
                color: AppTheme.roundIconColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopBottomBar(BuildContext context) {
    return Container(
      height: 40,
      child: Row(
        children: [
          const SizedBox(width: 16),
          _roleWidget(
            context,
            isBrokerCard ? AppStrings.broker : AppStrings.agent,
          ),
          const SizedBox(width: 12),
          _mainCardDesktopProfileBtn(),
          const Spacer(),
          Expanded(child: _recruitersSection(context)),
          const SizedBox(width: 12),
        ],
      ),
    );
  }

  Widget _mainCardDesktopProfileBtn() {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(6),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(6)),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              '$iconAssetpath/address_book.png',
              height: 14,
              width: 14,
              color: AppTheme.roundIconColor,
            ),
            const SizedBox(width: 6),
            Text(
              AppStrings.viewProfile,
              style: AppFonts.mediumTextStyle(
                14,
                color: AppTheme.roundIconColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///
  /// Agent list
  ///
  Widget _buildCompactCardLayout(BuildContext context) {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(15)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        mainAxisSize: MainAxisSize.min,

        children: [
          Expanded(flex: 3, child: _agentInfo(context)),
          Expanded(flex: 2, child: _roleRecruitsRow(context)),
        ],
      ),
    );
  }

  Widget _agentInfo(BuildContext context) {
    bool isDeskTop = Responsive.isDesktop(context);
    bool isMobile = Responsive.isMobile(context);
    bool isSmallMobile = Responsive.isSmallMobile(context);
    String name = isBrokerCard ? broker!.name : agent!.name;
    String email = isBrokerCard ? broker!.email : agent!.email;

    return Container(
      // color: Colors.amber,
      padding: EdgeInsets.symmetric(
        horizontal: isMobile ? defaultPadding / 2 : defaultPadding,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Profile image
          CircleAvatar(
            radius: isDeskTop ? 20 : 15,
            backgroundImage: AssetImage(agent!.imageUrl),
            backgroundColor: Colors.white.withValues(alpha: 0.2),
          ),

          SizedBox(width: isMobile ? defaultPadding / 2 : defaultPadding),

          // Agent details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  name,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: AppFonts.mediumTextStyle(
                    isDeskTop
                        ? 14
                        : isSmallMobile
                        ? 14
                        : 12,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Image.asset(
                      '$iconAssetpath/mail.png',
                      height: isDeskTop
                          ? 14
                          : isSmallMobile
                          ? 12
                          : 10,

                      width: isDeskTop
                          ? 14
                          : isSmallMobile
                          ? 12
                          : 10,
                      color: AppTheme.secondaryTextColor,
                    ),
                    SizedBox(width: isDeskTop ? 4 : 2),
                    Expanded(
                      child: Text(
                        email,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: AppFonts.mediumTextStyle(
                          isDeskTop
                              ? 12
                              : isSmallMobile
                              ? 12
                              : 10,
                          color: AppTheme.secondaryTextColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Arrow icon for navigation
          Image.asset(
            '$iconAssetpath/arrow_right.png',
            height: 16,
            width: 16,
            color: AppTheme.primaryTextColor,
          ),
        ],
      ),
    );
  }

  Widget _roleRecruitsRow(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        border: Border(top: BorderSide(color: AppTheme.borderColor, width: 1)),
      ),
      padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
      child: Row(
        children: [
          Expanded(flex: 1, child: _roleWidget(context, AppStrings.agent)),
          Expanded(flex: 2, child: _recruitersSection(context)),
        ],
      ),
    );
  }

  Widget _roleWidget(BuildContext context, String role) {
    bool isMobile = Responsive.isMobile(context);
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.headerIconBgColor,
          borderRadius: BorderRadius.circular(50),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: isMobile ? 4 : 8,
          vertical: isMobile ? 2 : 4,
        ),
        child: Text(
          role,
          style: AppFonts.mediumTextStyle(12, color: AppTheme.primaryTextColor),
        ),
      ),
    );
  }

  Widget _recruitersSection(BuildContext context) {
    bool isMobile = Responsive.isSmallMobile(context);
    return Container(
      margin: const EdgeInsets.only(right: 5),
      alignment: Alignment.centerRight,
      decoration: BoxDecoration(
        border: Border(left: BorderSide(color: AppTheme.borderColor, width: 1)),
      ),
      padding: EdgeInsets.only(
        left: defaultPadding / 2,
        top: isMobile ? 10 : 0,
        bottom: isMobile ? 10 : 0,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            '$iconAssetpath/user.png',
            height: 14,
            width: 14,
            color: AppTheme.primaryTextColor,
          ),
          const SizedBox(width: 4),
          RichText(
            text: TextSpan(
              style: AppFonts.mediumTextStyle(
                12,
                color: AppTheme.secondaryTextColor,
              ),
              children: [
                TextSpan(
                  text: isMobile
                      ? AppStrings.recruitsMobile
                      : AppStrings.recruitsCount,
                ),
                TextSpan(
                  text: "${agent?.agents.length}",
                  style: AppFonts.semiBoldTextStyle(
                    12,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
