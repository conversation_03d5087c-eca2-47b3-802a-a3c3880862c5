import 'package:flutter/material.dart';
import '/config/constants.dart';

class AuthBackground extends StatelessWidget {
  final Widget child;

  const AuthBackground({Key? key, required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('$imageAssetpath/auth_background.png'),
          fit: BoxFit.cover,
        ),
      ),
      child: child,
    );
  }
}
