import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../src/data/datasource/auth_data_source.dart';
import '../../src/presentation/cubit/auth/cubit/auth_cubit.dart';
import '../../theme/app_theme.dart';
import '/config/app_strings.dart';
import '/config/constants.dart';
import '/config/responsive.dart';
import '/theme/app_fonts.dart';
import '/screens/auth/components/auth_background.dart';
import '/screens/auth/components/social_login_button.dart';

import '/screens/auth/components/custom_checkbox.dart';
import '/screens/auth/components/primary_button.dart';
import '/screens/dashboard/dashboard_screen.dart';

class LoginScreen extends HookWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final emailFocusNode = FocusNode();
    final passwordFocusNode = FocusNode();
    final obscurePassword = useState(true);
    final rememberMeState = useState(false);
    final emailError = useState<String?>(null);
    final passwordError = useState<String?>(null);
    final formKey = useMemoized(() => GlobalKey<FormState>());

    final size = MediaQuery.of(context).size;
    return Scaffold(
      body: AuthBackground(
        child: Center(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: Responsive.isMobile(context) ? double.infinity : 900,
              minHeight: Responsive.isMobile(context)
                  ? size.height * 0.8
                  : size.height * 0.6,
            ),
            child: SingleChildScrollView(
              child: Card(
                color: Colors.transparent,
                elevation: Responsive.isMobile(context) ? 0 : 10,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    Responsive.isMobile(context) ? 0 : 5,
                  ),
                ),
                margin: EdgeInsets.symmetric(
                  horizontal: Responsive.isMobile(context) ? 0 : 40,
                  vertical: Responsive.isMobile(context) ? 0 : 20,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(
                    Responsive.isMobile(context) ? 0 : 5,
                  ),
                  child: IntrinsicHeight(
                    child: Responsive.isMobile(context)
                        ? LoginRightPanel(
                            isMobile: true,
                            emailController: emailController,
                            passwordController: passwordController,
                            obscurePassword: obscurePassword,
                            rememberMeState: rememberMeState,
                            emailError: emailError,
                            passwordError: passwordError,
                            formKey: formKey,
                            emailFocusNode: emailFocusNode,
                            passwordFocusNode: passwordFocusNode,
                          )
                        : Row(
                            children: [
                              if (!Responsive.isSmallMobile(context))
                                Expanded(
                                  flex: Responsive.isTablet(context) ? 4 : 5,
                                  child: const LoginLeftPanel(),
                                ),
                              Expanded(
                                flex: Responsive.isTablet(context) ? 6 : 5,
                                child: LoginRightPanel(
                                  isMobile: false,
                                  emailController: emailController,
                                  passwordController: passwordController,
                                  obscurePassword: obscurePassword,
                                  rememberMeState: rememberMeState,
                                  emailError: emailError,
                                  passwordError: passwordError,
                                  formKey: formKey,
                                  emailFocusNode: emailFocusNode,
                                  passwordFocusNode: passwordFocusNode,
                                ),
                              ),
                            ],
                          ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class LoginLeftPanel extends StatelessWidget {
  const LoginLeftPanel({super.key});

  @override
  Widget build(BuildContext context) {
    bool isDesktop = Responsive.isDesktop(context);
    bool isTablet = Responsive.isTablet(context);
    bool isMobile = Responsive.isMobile(context);
    return Container(
      padding: const EdgeInsets.all(defaultPadding * 2),
      decoration: BoxDecoration(
        color: AppTheme.loginBgColor.withValues(alpha: 0.65),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            blurRadius: 2,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Title with icon
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                '$iconAssetpath/login_title_icon.png',
                height: isDesktop ? 56 : 46,
                width: isDesktop ? 56 : 46,
              ),
              const SizedBox(width: 12),
              Text(
                appName,
                textAlign: TextAlign.center,
                style: AppFonts.boldTextStyle(
                  isDesktop ? 45 : 35,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: defaultPadding),

          RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              style: AppFonts.normalTextStyle(
                20,
                color: Colors.white,
              ).copyWith(height: 1.5),
              children: [
                TextSpan(text: appDescriptionP1),
                TextSpan(
                  text: appDescriptionP2,
                  style: AppFonts.semiBoldTextStyle(20, color: Colors.white),
                ),
                TextSpan(text: appDescriptionP3),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class LoginRightPanel extends StatelessWidget {
  final bool isMobile;
  final FocusNode emailFocusNode;
  final FocusNode passwordFocusNode;
  final TextEditingController emailController;
  final TextEditingController passwordController;
  final ValueNotifier<bool> obscurePassword;
  final ValueNotifier<bool> rememberMeState;
  final ValueNotifier<String?> emailError;
  final ValueNotifier<String?> passwordError;
  final GlobalKey<FormState> formKey;

  const LoginRightPanel({
    super.key,
    required this.isMobile,
    required this.emailController,
    required this.passwordController,
    required this.obscurePassword,
    required this.rememberMeState,
    required this.emailError,
    required this.passwordError,
    required this.formKey,
    required this.emailFocusNode,
    required this.passwordFocusNode,
  });

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  bool _isFormValid() {
    final email = emailController.text.trim();
    final password = passwordController.text.trim();

    return _validateEmail(email) == null && _validatePassword(password) == null;
  }

  Future<void> _handleLogin(BuildContext context) async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    final email = emailController.text.trim();
    final password = passwordController.text.trim();

    final payload = {'email': email, 'password': password};

    // context.read<AuthCubit>().login(payload);
    // final result = await AuthDataSource().login(payload);
    // if (result.success) {
    _navigateToDashboard(context);
    // } else {
    //   ScaffoldMessenger.of(
    //     context,
    //   ).showSnackBar(SnackBar(content: Text(result.message)));
    // }
  }

  Future<void> _handleGoogleLogin(BuildContext context) async {
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    try {
      // Implement Google Sign-In logic here
      // For now, simulate success
      await Future.delayed(const Duration(seconds: 1));

      // Hide loading indicator
      if (context.mounted) {
        Navigator.of(context).pop();
        _navigateToDashboard(context);
      }
    } catch (e) {
      // Hide loading indicator
      if (context.mounted) {
        Navigator.of(context).pop();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Google login failed: ${e.toString()}')),
        );
      }
    }
  }

  void _navigateToDashboard(BuildContext context) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) => const DashboardScreen()),
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 300;

    return Container(
      padding: EdgeInsets.all(
        Responsive.isMobile(context) ? defaultPadding : defaultPadding * 2,
      ),
      margin: EdgeInsets.all(
        Responsive.isMobile(context) ? defaultPadding * 2 : 0,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(
          Responsive.isMobile(context) ? 5 : 0,
        ),
      ),
      child: Form(
        key: formKey,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Show logo only on small mobile devices
            if (Responsive.isMobile(context)) ...[
              Text(
                appName,
                style: AppFonts.boldTextStyle(
                  28,
                  color: AppTheme.roundIconColor,
                ),
              ),
              const SizedBox(height: defaultPadding * 2),
            ],

            Text(
              loginTitle,
              style: AppFonts.semiBoldTextStyle(
                24,
                color: AppTheme.primaryTextColor,
              ),
            ),
            const SizedBox(height: defaultPadding * 2),

            SocialLoginButton(
              icon: Image.asset(
                '$iconAssetpath/google.png',
                height: 20,
                width: 20,
              ),
              text: signInWithGmail,
              onPressed: () {
                _handleGoogleLogin(context);
              },
            ),

            const SizedBox(height: defaultPadding * 1.8),

            Text(
              orContinueWith,
              style: AppFonts.regularTextStyle(
                14,
                color: AppTheme.orContinueWithColor,
              ),
            ),
            const SizedBox(height: defaultPadding * 1.8),

            // Email input field with real-time validation
            ValueListenableBuilder<String?>(
              valueListenable: emailError,
              builder: (context, errorText, child) {
                return TextFormField(
                  focusNode: emailFocusNode,
                  controller: emailController,
                  keyboardType: TextInputType.emailAddress,
                  validator: _validateEmail,
                  autovalidateMode: AutovalidateMode.onUnfocus,
                  onChanged: (value) {
                    if (emailFocusNode.hasFocus) {
                      emailError.value = null;
                    } else {
                      final error = _validatePassword(value);
                      emailError.value = error;
                    }
                  },
                  style: AppFonts.regularTextStyle(
                    Responsive.isMobile(context) ? 16 : 14,
                    color: AppTheme.primaryTextColor,
                  ),
                  decoration: InputDecoration(
                    hintText: emailHint,
                    hintStyle: AppFonts.regularTextStyle(
                      Responsive.isMobile(context) ? 16 : 14,
                      color: AppTheme.textFieldHint,
                    ),
                    errorText: errorText,
                    filled: true,
                    fillColor: Colors.grey.shade50,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide: BorderSide(color: AppTheme.textFieldBorder),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide: BorderSide(
                        color: errorText != null
                            ? Colors.red
                            : AppTheme.textFieldBorder,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide: BorderSide(
                        color: errorText != null
                            ? Colors.red
                            : AppTheme.primaryColor,
                        width: 2,
                      ),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide: const BorderSide(color: Colors.red, width: 1),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide: const BorderSide(color: Colors.red, width: 2),
                    ),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: Responsive.isMobile(context) ? 18 : 16,
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: defaultPadding),

            // Password input field with real-time validation
            ValueListenableBuilder<String?>(
              valueListenable: passwordError,
              builder: (context, errorText, child) {
                return ValueListenableBuilder<bool>(
                  valueListenable: obscurePassword,
                  builder: (context, isObscured, child) {
                    return TextFormField(
                      focusNode: passwordFocusNode,
                      controller: passwordController,
                      obscureText: isObscured,
                      validator: _validatePassword,
                      autovalidateMode: AutovalidateMode.onUnfocus,
                      onChanged: (value) {
                        // Real-time validation
                        if (passwordFocusNode.hasFocus) {
                          passwordError.value = null;
                        } else {
                          final error = _validatePassword(value);
                          passwordError.value = error;
                        }
                      },
                      style: AppFonts.regularTextStyle(
                        Responsive.isMobile(context) ? 16 : 14,
                        color: AppTheme.primaryTextColor,
                      ),
                      decoration: InputDecoration(
                        hintText: passwordHint,
                        hintStyle: AppFonts.regularTextStyle(
                          Responsive.isMobile(context) ? 16 : 14,
                          color: AppTheme.textFieldHint,
                        ),
                        errorText: errorText,
                        suffixIcon: IconButton(
                          icon: Icon(
                            isObscured
                                ? Icons.visibility_off
                                : Icons.visibility,
                            color: Colors.grey,
                            size: 20,
                          ),
                          onPressed: () {
                            obscurePassword.value = !obscurePassword.value;
                          },
                        ),
                        filled: true,
                        fillColor: Colors.grey.shade50,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(25),
                          borderSide: BorderSide(
                            color: AppTheme.textFieldBorder,
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(25),
                          borderSide: BorderSide(
                            color: errorText != null
                                ? Colors.red
                                : AppTheme.textFieldBorder,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(25),
                          borderSide: BorderSide(
                            color: errorText != null
                                ? Colors.red
                                : AppTheme.primaryColor,
                            width: 2,
                          ),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(25),
                          borderSide: const BorderSide(
                            color: Colors.red,
                            width: 1,
                          ),
                        ),
                        focusedErrorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(25),
                          borderSide: const BorderSide(
                            color: Colors.red,
                            width: 2,
                          ),
                        ),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: Responsive.isMobile(context) ? 18 : 16,
                        ),
                      ),
                    );
                  },
                );
              },
            ),

            const SizedBox(height: 8),

            // Remember me checkbox and forgot password
            ValueListenableBuilder<bool>(
              valueListenable: rememberMeState,
              builder: (context, isChecked, child) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomCheckbox(
                      value: isChecked,
                      onChanged: (value) {
                        rememberMeState.value = value ?? false;
                      },
                      text: rememberMe,
                    ),
                    GestureDetector(
                      onTap: () {
                        // Handle forgot password
                      },
                      child: Text(
                        forgotPassword,
                        style: AppFonts.mediumTextStyle(
                          14,
                          color: AppTheme.roundIconColor,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),

            const SizedBox(height: defaultPadding * 1.5),

            // Login button
            BlocConsumer<AuthCubit, AuthState>(
              listener: (context, state) {
                if (state is AuthSuccess) {
                  _navigateToDashboard(context);
                } else if (state is AuthError) {
                  ScaffoldMessenger.of(
                    context,
                  ).showSnackBar(SnackBar(content: Text(state.message)));
                }
              },
              builder: (context, state) {
                return AnimatedBuilder(
                  animation: Listenable.merge([
                    emailController,
                    passwordController,
                  ]),
                  builder: (context, child) {
                    final isFormValid = _isFormValid();
                    final isLoading = state is AuthLoading;

                    return PrimaryButton(
                      text: isLoading ? 'Logging in...' : loginButton,
                      height: 45,
                      borderRadius: 25,
                      onPressed: (isLoading || !isFormValid)
                          ? null
                          : () async => await _handleLogin(context),
                    );
                  },
                );
              },
            ),

            const SizedBox(height: defaultPadding * 2),
          ],
        ),
      ),
    );
  }
}
