import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../config/app_strings.dart';
import '../../config/constants.dart';
import '../../config/responsive.dart';
import '../../theme/app_theme.dart';
import '../../theme/app_fonts.dart';
import '../dashboard/components/header.dart';
import 'components/agents_table.dart';

class AgentsScreen extends HookWidget {
  const AgentsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final header = Header(selectedTab: agentsTab);

    return Scaffold(
      drawer: header.mobileDrawer,
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.fromLTRB(
            Responsive.isMobile(context) ? 8 : webLayoutmargin,
            Responsive.isMobile(context) ? 8 : defaultMargin,
            Responsive.isMobile(context) ? 8 : webLayoutmargin,
            0,
          ),
          child: Column(
            children: [
              header,
              const SizedBox(height: defaultPadding),
              Expanded(
                child: SingleChildScrollView(
                  child: Container(
                    // padding: const EdgeInsets.all(defaultPadding),
                    // decoration: BoxDecoration(
                    //   color: AppTheme.white, //Colors.white,
                    //   borderRadius: BorderRadius.circular(10),
                    // ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: defaultPadding),
                        const AgentsTable(),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
