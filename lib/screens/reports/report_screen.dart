import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/models/report_model.dart';
import 'package:neorevv/theme/app_theme.dart';
import '/config/constants.dart';
import '/theme/app_fonts.dart';
import 'components/report_sidebar.dart';
import 'components/pdf_preview_panel.dart';

class ReportScreen extends HookWidget {
  const ReportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final selectedReport = useState<ReportModel?>(null);

    // Sample report data matching the design
    final reports = useState<List<ReportModel>>([
      ReportModel(
        id: '1',
        title: 'Brokerage Sales Volume Report',
        icon: Icons.bar_chart,
        isSelected: true,
      ),
      ReportModel(
        id: '2',
        title: 'Commission Distribution Report',
        icon: Icons.pie_chart,
        isSelected: false,
      ),
      ReportModel(
        id: '3',
        title: 'Transaction Status Report',
        icon: Icons.list_alt,
        isSelected: false,
      ),
      ReportModel(
        id: '4',
        title: 'Listing Activity Report',
        icon: Icons.home_work,
        isSelected: false,
      ),
      ReportModel(
        id: '5',
        title: 'Escrow and Deposits Report',
        icon: Icons.account_balance,
        isSelected: false,
      ),
      ReportModel(
        id: '6',
        title: 'Closing Timeline Report',
        icon: Icons.timeline,
        isSelected: false,
      ),
      ReportModel(
        id: '7',
        title: 'Lead Source Effectiveness Report',
        icon: Icons.trending_up,
        isSelected: false,
      ),
      ReportModel(
        id: '8',
        title: 'Legal & Compliance Report',
        icon: Icons.gavel,
        isSelected: false,
      ),
      ReportModel(
        id: '9',
        title: 'Top Producers Report',
        icon: Icons.star,
        isSelected: false,
      ),
    ]);

    // Set initial selected report
    useEffect(() {
      selectedReport.value = reports.value.firstWhere((r) => r.isSelected);
      return null;
    }, []);

    void onReportSelected(ReportModel report) {
      // Update selection state
      final updatedReports = reports.value.map((r) {
        return r.copyWith(isSelected: r.id == report.id);
      }).toList();

      reports.value = updatedReports;
      selectedReport.value = report;
    }

    return Scaffold(
      backgroundColor: AppTheme.scaffoldBgColor,
      body: Column(
        children: [
          // Top Navigation Bar
          Container(
            height: 60,
            color: Colors.white,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
              child: Row(
                children: [
                  // Logo
                  Text(
                    'NeoRevv',
                    style: AppFonts.boldTextStyle(24, color: AppTheme.primaryTextColor),
                  ),
                  const SizedBox(width: 40),

                  // Navigation Items
                  _buildNavItem('Dashboard', false),
                  _buildNavItem('Brokers', false),
                  _buildNavItem('Agents', false),
                  _buildNavItem('Sales', false),
                  _buildNavItem('Commission', false),
                  _buildNavItem('Reports', true),
                  _buildNavItem('Community', false),

                  const Spacer(),

                  // Right side icons and user
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.notifications_outlined),
                        onPressed: () {},
                      ),
                      IconButton(
                        icon: const Icon(Icons.settings_outlined),
                        onPressed: () {},
                      ),
                      const SizedBox(width: 8),
                      Row(
                        children: [
                          const CircleAvatar(
                            radius: 16,
                            backgroundColor: AppTheme.primaryColor,
                            child: Icon(Icons.person, color: Colors.white, size: 18),
                          ),
                          const SizedBox(width: 8),
                          Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Nabii',
                                style: AppFonts.mediumTextStyle(14, color: AppTheme.primaryTextColor),
                              ),
                              Text(
                                'Platform Owner',
                                style: AppFonts.regularTextStyle(12, color: Colors.grey),
                              ),
                            ],
                          ),
                          const Icon(Icons.keyboard_arrow_down),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Breadcrumb
          Container(
            color: Colors.white,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: defaultPadding, vertical: 8),
              child: Row(
                children: [
                  const Icon(Icons.home, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    'Dashboard Admin',
                    style: AppFonts.regularTextStyle(14, color: Colors.grey),
                  ),
                  const Icon(Icons.chevron_right, size: 16, color: Colors.grey),
                  Text(
                    'Reports',
                    style: AppFonts.regularTextStyle(14, color: AppTheme.primaryColor),
                  ),
                ],
              ),
            ),
          ),

          // Main Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(defaultPadding),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Left Sidebar - Report List
                  SizedBox(
                    width: 280,
                    child: ReportSidebar(
                      reports: reports.value,
                      onReportSelected: onReportSelected,
                    ),
                  ),

                  const SizedBox(width: defaultPadding),

                  // Right Panel - PDF Preview
                  Expanded(
                    child: selectedReport.value != null
                        ? PdfPreviewPanel(
                            report: selectedReport.value!,
                          )
                        : const Center(
                            child: Text('Select a report to preview'),
                          ),
                  ),
                ],
              ),
            ),
          ),

          // Footer
          Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Copyright © 2025 NeoRevv',
                    style: AppFonts.regularTextStyle(12, color: Colors.grey),
                  ),
                  const SizedBox(width: 20),
                  Text(
                    'Home',
                    style: AppFonts.regularTextStyle(12, color: Colors.grey),
                  ),
                  const SizedBox(width: 20),
                  Text(
                    'Privacy Policy',
                    style: AppFonts.regularTextStyle(12, color: Colors.grey),
                  ),
                  const SizedBox(width: 20),
                  Text(
                    'Term and conditions',
                    style: AppFonts.regularTextStyle(12, color: Colors.grey),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem(String title, bool isActive) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title,
            style: AppFonts.mediumTextStyle(
              14,
              color: isActive ? AppTheme.primaryColor : AppTheme.primaryTextColor,
            ),
          ),
          if (isActive)
            Container(
              margin: const EdgeInsets.only(top: 4),
              height: 2,
              width: 40,
              color: AppTheme.primaryColor,
            ),
        ],
      ),
    );
  }
}
