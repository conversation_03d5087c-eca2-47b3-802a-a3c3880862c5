// import 'dart:io';
// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:file_picker/file_picker.dart';
// import 'package:neorevv/models/report_model.dart';
// import 'package:neorevv/theme/app_theme.dart';
// import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
// import '/config/constants.dart';
// import '/theme/app_fonts.dart';

// class PdfPreviewPanel extends HookWidget {
//   final ReportModel report;

//   const PdfPreviewPanel({
//     super.key,
//     required this.report,
//   });

//   @override
//   Widget build(BuildContext context) {
//     final pdfPath = useState<String?>(null);
//     final isLoading = useState<bool>(false);
//     final pdfViewerController = PdfViewerController();

//     Future<void> pickPdfFile() async {
//       try {
//         isLoading.value = true;
//         FilePickerResult? result = await FilePicker.platform.pickFiles(
//           type: FileType.custom,
//           allowedExtensions: ['pdf'],
//           allowMultiple: false,
//         );

//         if (result != null && result.files.single.path != null) {
//           pdfPath.value = result.files.single.path;
//         }
//       } catch (e) {
//         if (context.mounted) {
//           ScaffoldMessenger.of(context).showSnackBar(
//             SnackBar(content: Text('Error picking file: $e')),
//           );
//         }
//       } finally {
//         isLoading.value = false;
//       }
//     }

//     return Container(
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(12),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.05),
//             blurRadius: 10,
//             offset: const Offset(0, 2),
//           ),
//         ],
//       ),
//       child: Column(
//         children: [
//           // Header
//           Container(
//             padding: const EdgeInsets.all(defaultPadding),
//             decoration: const BoxDecoration(
//               border: Border(
//                 bottom: BorderSide(color: AppTheme.borderColor),
//               ),
//             ),
//             child: Row(
//               children: [
//                 Expanded(
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Text(
//                         report.title,
//                         style: AppFonts.semiBoldTextStyle(
//                           18,
//                           color: AppTheme.primaryTextColor,
//                         ),
//                       ),
//                       const SizedBox(height: 4),
//                       Text(
//                         'A comprehensive analysis based on sales volume report data. This report has been generated from the data.',
//                         style: AppFonts.regularTextStyle(
//                           12,
//                           color: Colors.grey[600],
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//                 const SizedBox(width: 16),
                
//                 // PDF Badge
//                 Container(
//                   padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
//                   decoration: BoxDecoration(
//                     color: Colors.red,
//                     borderRadius: BorderRadius.circular(4),
//                   ),
//                   child: Text(
//                     'PDF',
//                     style: AppFonts.semiBoldTextStyle(10, color: Colors.white),
//                   ),
//                 ),
//                 const SizedBox(width: 8),
//                 Text(
//                   '${report.title}.pdf',
//                   style: AppFonts.mediumTextStyle(12, color: AppTheme.primaryTextColor),
//                 ),
//               ],
//             ),
//           ),
          
//           // Action Buttons
//           Container(
//             padding: const EdgeInsets.symmetric(horizontal: defaultPadding, vertical: 12),
//             decoration: const BoxDecoration(
//               border: Border(
//                 bottom: BorderSide(color: AppTheme.borderColor),
//               ),
//             ),
//             child: Row(
//               children: [
//                 ElevatedButton(
//                   onPressed: pickPdfFile,
//                   style: ElevatedButton.styleFrom(
//                     backgroundColor: AppTheme.primaryColor,
//                     foregroundColor: Colors.white,
//                     padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(6),
//                     ),
//                   ),
//                   child: Text(
//                     'Preview',
//                     style: AppFonts.mediumTextStyle(14, color: Colors.white),
//                   ),
//                 ),
//                 const SizedBox(width: 12),
//                 OutlinedButton(
//                   onPressed: () {
//                     // Handle download
//                     ScaffoldMessenger.of(context).showSnackBar(
//                       const SnackBar(content: Text('Download functionality')),
//                     );
//                   },
//                   style: OutlinedButton.styleFrom(
//                     foregroundColor: AppTheme.primaryColor,
//                     side: const BorderSide(color: AppTheme.primaryColor),
//                     padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(6),
//                     ),
//                   ),
//                   child: Text(
//                     'Download',
//                     style: AppFonts.mediumTextStyle(14, color: AppTheme.primaryColor),
//                   ),
//                 ),
//               ],
//             ),
//           ),
          
//           // PDF Viewer or Placeholder
//           Expanded(
//             child: Container(
//               margin: const EdgeInsets.all(defaultPadding),
//               decoration: BoxDecoration(
//                 color: Colors.grey[50],
//                 borderRadius: BorderRadius.circular(8),
//                 border: Border.all(color: Colors.grey[200]!),
//               ),
//               child: pdfPath.value != null
//                   ? ClipRRect(
//                       borderRadius: BorderRadius.circular(8),
//                       child: SfPdfViewer.file(
//                         File(pdfPath.value!),
//                         controller: pdfViewerController,
//                         canShowScrollHead: false,
//                         canShowScrollStatus: false,
//                       ),
//                     )
//                   : _buildPdfPlaceholder(),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildPdfPlaceholder() {
//     return Container(
//       padding: const EdgeInsets.all(defaultPadding * 2),
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           // Mock PDF Preview Content
//           Container(
//             width: double.infinity,
//             height: 300,
//             decoration: BoxDecoration(
//               color: Colors.white,
//               borderRadius: BorderRadius.circular(8),
//               border: Border.all(color: Colors.grey[300]!),
//             ),
//             child: Column(
//               children: [
//                 // Mock PDF Header
//                 Container(
//                   padding: const EdgeInsets.all(16),
//                   child: Column(
//                     children: [
//                       Text(
//                         'Brokerage Sales Volume Report',
//                         style: AppFonts.boldTextStyle(16, color: AppTheme.primaryTextColor),
//                         textAlign: TextAlign.center,
//                       ),
//                       const SizedBox(height: 8),
//                       Text(
//                         'A comprehensive analysis based on sales volume report data. This report has been generated from the data.',
//                         style: AppFonts.regularTextStyle(12, color: Colors.grey[600]),
//                         textAlign: TextAlign.center,
//                       ),
//                     ],
//                   ),
//                 ),
                
//                 // Mock Chart/Content Area
//                 Expanded(
//                   child: Container(
//                     margin: const EdgeInsets.all(16),
//                     decoration: BoxDecoration(
//                       color: Colors.grey[100],
//                       borderRadius: BorderRadius.circular(4),
//                     ),
//                     child: Center(
//                       child: Column(
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         children: [
//                           Icon(
//                             Icons.insert_chart,
//                             size: 48,
//                             color: Colors.grey[400],
//                           ),
//                           const SizedBox(height: 8),
//                           Text(
//                             'Chart and Data Visualization',
//                             style: AppFonts.regularTextStyle(12, color: Colors.grey[500]),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ),
          
//           const SizedBox(height: 24),
          
//           Text(
//             'Click "Preview" to select and view a PDF file',
//             style: AppFonts.regularTextStyle(14, color: Colors.grey[600]),
//             textAlign: TextAlign.center,
//           ),
//         ],
//       ),
//     );
//   }
// }
