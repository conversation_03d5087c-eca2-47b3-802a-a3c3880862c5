import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/config/json_consts.dart';
import 'package:neorevv/models/sales.dart';
import 'package:neorevv/screens/dashboard/components/header.dart';
import 'package:neorevv/screens/sales/sales_review_doc_screen.dart';
import 'package:neorevv/theme/app_theme.dart';
import 'package:neorevv/utils/responsive.dart';
import 'package:neorevv/config/constants.dart';

class SalesTableScreen extends HookWidget {
  const SalesTableScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final currentPage = useState(1);
    final itemsPerPage = 10;
    final totalItems = salesList.length;
    final totalPages = (totalItems / itemsPerPage).ceil();

    // Get current page items
    final startIndex = (currentPage.value - 1) * itemsPerPage;
    final endIndex = (startIndex + itemsPerPage).clamp(0, totalItems);
    final currentPageItems = salesList.sublist(startIndex, endIndex);

    return Scaffold(
      backgroundColor: AppTheme.scaffoldBgColor,
      body: SafeArea(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Main content
            Expanded(
              flex: 5,
              child: Column(
                children: [
                  Header(selectedTab: 'Sales'),
                  const SizedBox(height: defaultPadding),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(defaultPadding),
                      child: Column(
                        children: [
                          _buildSalesTable(context, currentPageItems, currentPage, totalPages),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesTable(BuildContext context, List<Sales> salesData, ValueNotifier<int> currentPage, int totalPages) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: AppTheme.borderColor,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.trending_up,
                      color: AppTheme.primaryColor,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Sales',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.primaryTextColor,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: AppTheme.searchbarBg,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: const Row(
                        children: [
                          Icon(
                            Icons.filter_list,
                            size: 16,
                            color: AppTheme.secondaryTextColor,
                          ),
                          SizedBox(width: 4),
                          Text(
                            'Filter',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppTheme.secondaryTextColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 12),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: AppTheme.searchbarBg,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: const Row(
                        children: [
                          Icon(
                            Icons.download,
                            size: 16,
                            color: AppTheme.secondaryTextColor,
                          ),
                          SizedBox(width: 4),
                          Text(
                            'Export',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppTheme.secondaryTextColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Table
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Container(
                width: 1400, // Fixed width to accommodate all columns
                child: DataTable(
                  headingRowHeight: 50,
                  dataRowHeight: 60,
                  headingTextStyle: const TextStyle(
                    color: AppTheme.tableColumnHeaderColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  dataTextStyle: const TextStyle(
                    color: AppTheme.primaryTextColor,
                    fontSize: 12,
                  ),
                  columns: const [
                    DataColumn(label: Text('Transaction ID')),
                    DataColumn(label: Text('Agent Name')),
                    DataColumn(label: Text('Property Type')),
                    DataColumn(label: Text('Property Address')),
                    DataColumn(label: Text('Property Value')),
                    DataColumn(label: Text('Buyer Name')),
                    DataColumn(label: Text('Buyer Address')),
                    DataColumn(label: Text('Listing Date')),
                    DataColumn(label: Text('Sale Date')),
                    DataColumn(label: Text('Sale Price')),
                    DataColumn(label: Text('Commission')),
                    DataColumn(label: Text('Commission Amt')),
                    DataColumn(label: Text('Actions')),
                  ],
                  rows: salesData.map((sale) => _buildDataRow(context, sale)).toList(),
                ),
              ),
            ),
          ),

          // Pagination
          Container(
            padding: const EdgeInsets.all(20),
            decoration: const BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: AppTheme.borderColor,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Showing ${(currentPage.value - 1) * 10 + 1} to ${(currentPage.value * 10).clamp(0, salesList.length)} of ${salesList.length} entries',
                  style: const TextStyle(
                    color: AppTheme.secondaryTextColor,
                    fontSize: 14,
                  ),
                ),
                Row(
                  children: [
                    InkWell(
                      onTap: currentPage.value > 1 ? () => currentPage.value-- : null,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: AppTheme.paginationInactiveBg,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Row(
                          children: [
                            Icon(Icons.arrow_back_ios, size: 12, color: AppTheme.primaryTextColor),
                            SizedBox(width: 4),
                            Text('Previous', style: TextStyle(color: AppTheme.primaryTextColor, fontSize: 14)),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    for (int i = 1; i <= totalPages && i <= 5; i++)
                      Container(
                        margin: const EdgeInsets.only(right: 8),
                        child: InkWell(
                          onTap: () => currentPage.value = i,
                          child: Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: currentPage.value == i ? AppTheme.paginationActiveBg : AppTheme.paginationInactiveBg,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Center(
                              child: Text(
                                i.toString(),
                                style: TextStyle(
                                  color: currentPage.value == i ? AppTheme.white : AppTheme.primaryTextColor,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    const SizedBox(width: 16),
                    InkWell(
                      onTap: currentPage.value < totalPages ? () => currentPage.value++ : null,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: AppTheme.paginationInactiveBg,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Row(
                          children: [
                            Text('Next', style: TextStyle(color: AppTheme.primaryTextColor, fontSize: 14)),
                            SizedBox(width: 4),
                            Icon(Icons.arrow_forward_ios, size: 12, color: AppTheme.primaryTextColor),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  DataRow _buildDataRow(BuildContext context, Sales sale) {
    return DataRow(
      cells: [
        DataCell(Text(sale.transactionId, overflow: TextOverflow.ellipsis)),
        DataCell(Text(sale.agentName, overflow: TextOverflow.ellipsis)),
        DataCell(Text(sale.propertyType, overflow: TextOverflow.ellipsis)),
        DataCell(Text(sale.propertyAddress, overflow: TextOverflow.ellipsis)),
        DataCell(Text(sale.propertyValue, overflow: TextOverflow.ellipsis)),
        DataCell(Text(sale.buyerName, overflow: TextOverflow.ellipsis)),
        DataCell(Text(sale.buyerAddress, overflow: TextOverflow.ellipsis)),
        DataCell(Text(sale.listingDate, overflow: TextOverflow.ellipsis)),
        DataCell(Text(sale.saleDate, overflow: TextOverflow.ellipsis)),
        DataCell(Text(sale.salePrice, overflow: TextOverflow.ellipsis)),
        DataCell(Text(sale.commission, overflow: TextOverflow.ellipsis)),
        DataCell(Text(sale.commissionAmount, overflow: TextOverflow.ellipsis)),
        DataCell(
          InkWell(
            onTap: () {
              // Navigate to the existing sales review document screen
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => SalesReviewDocScreen(enableEditing: false),
                ),
              );
            },
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Image.asset(
                '$iconAssetpath/eye.png',
                height: 16,
                width: 16,
                color: AppTheme.primaryColor,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
