import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/screens/dashboard/components/header.dart';
import 'package:neorevv/screens/sales/components/sales_table.dart';
import 'package:neorevv/theme/app_theme.dart';
import 'package:neorevv/config/constants.dart';

class SalesTableScreen extends HookWidget {
  const SalesTableScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.scaffoldBgColor,
      body: SafeArea(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Main content
            Expanded(
              flex: 5,
              child: Column(
                children: [
                  Header(selectedTab: 'Sales'),
                  const SizedBox(height: defaultPadding),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(defaultPadding),
                      child: const SalesTable(),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

}
