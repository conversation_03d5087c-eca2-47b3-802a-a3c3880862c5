import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/screens/dashboard/components/rounderd_icon_btn.dart';
import 'dart:math' as math;
import '../../../theme/app_theme.dart';
import '../../../theme/app_fonts.dart';
import '../../../config/constants.dart';
import '../../../config/responsive.dart';
import '../../../config/app_strings.dart';
import '../../../models/sales.dart';
import '../../../config/json_consts.dart';
import '../../dashboard/components/brokers_table.dart';
import '../../dashboard/components/dashboard_content.dart';
import '../sales_review_doc_screen.dart';

class SalesTable extends HookWidget {
  const SalesTable({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final currentPage = useState(1);
    final itemsPerPage = useState(10);
    final searchQuery = useState('');
    final showFilter = useState(false);
    final selectedAgent = useState<String?>(null);
    final selectedPropertyType = useState<String?>(null);
    final selectedStatus = useState<String?>(null);
    final showTooltip = useState(false);

    // Applied filters - these are used for tooltip and actual filtering
    final appliedAgent = useState<String?>(null);
    final appliedPropertyType = useState<String?>(null);
    final appliedStatus = useState<String?>(null);

    //TODO: need to integrate API
    List<Sales> getFilteredSales() {
      List<Sales> filtered = salesList;

      if (searchQuery.value.isNotEmpty) {
        filtered = filtered
            .where(
              (sale) => sale.agentName.toLowerCase().contains(
                searchQuery.value.toLowerCase(),
              ) ||
              sale.buyerName.toLowerCase().contains(
                searchQuery.value.toLowerCase(),
              ) ||
              sale.transactionId.toLowerCase().contains(
                searchQuery.value.toLowerCase(),
              ),
            )
            .toList();
      }

      // Apply filters using the applied filter values (set when Apply button is clicked)
      if (appliedAgent.value != null && appliedAgent.value!.isNotEmpty) {
        filtered = filtered
            .where((sale) => sale.agentName == appliedAgent.value)
            .toList();
      }

      if (appliedPropertyType.value != null && appliedPropertyType.value!.isNotEmpty) {
        filtered = filtered
            .where((sale) => sale.propertyType == appliedPropertyType.value)
            .toList();
      }

      if (appliedStatus.value != null && appliedStatus.value!.isNotEmpty) {
        // Add status filtering logic here if needed
      }

      return filtered;
    }

    List<Sales> getPaginatedSales() {
      final filtered = getFilteredSales();
      final startIndex = (currentPage.value - 1) * itemsPerPage.value;
      final endIndex = (startIndex + itemsPerPage.value).clamp(
        0,
        filtered.length,
      );
      return filtered.sublist(startIndex, endIndex);
    }

    int getTotalPages() =>
        (getFilteredSales().length / itemsPerPage.value).ceil();

    return LayoutBuilder(
      builder: (context, constraints) {
        return Column(
          children: [
            Container(
              width: constraints.maxWidth,
              padding: const EdgeInsets.only(bottom: defaultPadding - 2),
              decoration: BoxDecoration(
                color: AppTheme.white,
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.black.withValues(alpha: 0.05),
                    blurRadius: 5,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (showFilter.value)
                      _buildFilterWidget(
                        context,
                        selectedAgent,
                        selectedPropertyType,
                        selectedStatus,
                        showFilter,
                        currentPage,
                        showTooltip,
                        appliedAgent,
                        appliedPropertyType,
                        appliedStatus,
                      ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(
                        defaultPadding * 1.5,
                        defaultPadding - 2,
                        defaultPadding * 1.5,
                        0,
                      ),
                      child: _buildHeader(
                        context,
                        searchQuery,
                        showFilter,
                        selectedAgent,
                        selectedPropertyType,
                        selectedStatus,
                        showTooltip,
                        appliedAgent,
                        appliedPropertyType,
                        appliedStatus,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: defaultPadding * 1.5,
                      ),
                      child: _buildTable(
                        context,
                        constraints,
                        getPaginatedSales(),
                      ),
                    ),
                    const SizedBox(height: defaultPadding),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: defaultPadding * 1.5,
                      ),
                      child: _buildFooter(
                        context,
                        currentPage,
                        itemsPerPage,
                        getFilteredSales(),
                        getTotalPages(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: defaultPadding * 2),
            const Footer(),
          ],
        );
      },
    );
  }

  Widget _buildHeader(
    BuildContext context,
    ValueNotifier<String> searchQuery,
    ValueNotifier<bool> showFilter,
    ValueNotifier<String?> selectedAgent,
    ValueNotifier<String?> selectedPropertyType,
    ValueNotifier<String?> selectedStatus,
    ValueNotifier<bool> showTooltip,
    ValueNotifier<String?> appliedAgent,
    ValueNotifier<String?> appliedPropertyType,
    ValueNotifier<String?> appliedStatus,
  ) {
    // For very small mobile devices, use a column layout
    if (Responsive.isSmallMobile(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              RoundIconBtn(
                icon: 'sales',
                backgroundColor: Colors.transparent,
                onPressed: () {},
                iconSize: 20,
              ),
              const SizedBox(width: 5),
              Expanded(
                child: Text('Sales', style: AppFonts.semiBoldTextStyle(18)),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 16),
                child: _buildFilterResultsTooltip(
                  appliedAgent.value,
                  appliedPropertyType.value,
                  appliedStatus.value,
                  appliedAgent,
                  appliedPropertyType,
                  appliedStatus,
                  showTooltip,
                  selectedAgent,
                  selectedPropertyType,
                  selectedStatus,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: () {
                    showFilter.value = !showFilter.value;
                    if (showFilter.value) {
                      // When opening filter, restore previously selected values
                      selectedAgent.value = appliedAgent.value;
                      selectedPropertyType.value = appliedPropertyType.value;
                      selectedStatus.value = appliedStatus.value;
                    }
                  },
                  child: Container(
                    width: ResponsiveSizes.filterButtonWidth(context),
                    height: 40,
                    decoration: BoxDecoration(
                      color: showFilter.value
                          ? AppTheme.selectedComboBoxBorder
                          : AppTheme.searchbarBg,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: Image.asset(
                            '$iconAssetpath/filter.png',
                            color: showFilter.value
                                ? Colors.white
                                : AppTheme.tableDataFont,
                          ),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          filter,
                          style: AppFonts.regularTextStyle(
                            14,
                            color: showFilter.value
                                ? Colors.white
                                : AppTheme.tableDataFont,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: SizedBox(
                  height: 40,
                  child: TextField(
                    onChanged: (value) => searchQuery.value = value,
                    decoration: InputDecoration(
                      hintText: 'Search Sales',
                      hintStyle: AppFonts.regularTextStyle(
                        14,
                        color: AppTheme.tableDataFont,
                      ),
                      prefixIcon: Container(
                        height: 24,
                        width: 24,
                        padding: const EdgeInsets.only(
                          left: 8,
                          top: 8,
                          bottom: 8,
                        ),
                        child: Image.asset('$iconAssetpath/search.png'),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: AppTheme.searchbarBg,
                      contentPadding: const EdgeInsets.symmetric(vertical: 0),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      );
    }

    // For larger screens, use the original row layout
    return Row(
      children: [
        Row(
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: Image.asset('$iconAssetpath/sales.png'),
            ),
            const SizedBox(width: 8),
            Text('Sales', style: AppFonts.semiBoldTextStyle(22)),
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: _buildFilterResultsTooltip(
                appliedAgent.value,
                appliedPropertyType.value,
                appliedStatus.value,
                appliedAgent,
                appliedPropertyType,
                appliedStatus,
                showTooltip,
                selectedAgent,
                selectedPropertyType,
                selectedStatus,
              ),
            ),
          ],
        ),
        const Spacer(),
        Row(
          children: [
            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () => showFilter.value = !showFilter.value,
                child: Container(
                  width: ResponsiveSizes.filterButtonWidth(context),
                  height: 40,
                  decoration: BoxDecoration(
                    color: showFilter.value
                        ? AppTheme.selectedComboBoxBorder
                        : AppTheme.searchbarBg,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: Image.asset(
                          '$iconAssetpath/filter.png',
                          color: showFilter.value
                              ? Colors.white
                              : AppTheme.tableDataFont,
                        ),
                      ),
                      const SizedBox(width: 6),
                      Text(
                        filter,
                        style: AppFonts.regularTextStyle(
                          14,
                          color: showFilter.value
                              ? Colors.white
                              : AppTheme.tableDataFont,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            SizedBox(
              width: ResponsiveSizes.searchFieldWidth(context),
              height: 40,
              child: TextField(
                onChanged: (value) => searchQuery.value = value,
                decoration: InputDecoration(
                  hintText: 'Search Sales',
                  hintStyle: AppFonts.regularTextStyle(
                    14,
                    color: AppTheme.tableDataFont,
                  ),
                  prefixIcon: Container(
                    height: 24,
                    width: 24,
                    padding: const EdgeInsets.only(left: 8, top: 8, bottom: 8),
                    child: Image.asset('$iconAssetpath/search.png'),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(20),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: AppTheme.searchbarBg,
                  contentPadding: const EdgeInsets.symmetric(vertical: 0),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFilterResultsTooltip(
    String? agent,
    String? propertyType,
    String? status,
    ValueNotifier<String?> appliedAgent,
    ValueNotifier<String?> appliedPropertyType,
    ValueNotifier<String?> appliedStatus,
    ValueNotifier<bool> showTooltip,
    ValueNotifier<String?> selectedAgent,
    ValueNotifier<String?> selectedPropertyType,
    ValueNotifier<String?> selectedStatus,
  ) {
    List<String> filters = [];
    if (agent != null) filters.add("Agent - $agent");
    if (propertyType != null) filters.add("Property Type - $propertyType");
    if (status != null) filters.add("Status - $status");

    // Only show tooltip if showTooltip is true and filters are not empty
    if (filters.isEmpty || !showTooltip.value) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppTheme.applyFilterTooltip,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          RichText(
            text: TextSpan(
              style: AppFonts.regularTextStyle(12, color: Colors.black),
              children: [
                const TextSpan(text: filterResult),
                ...filters
                    .map(
                      (filter) => TextSpan(
                        text: filter,
                        style: AppFonts.boldTextStyle(12, color: Colors.black),
                      ),
                    )
                    .expand(
                      (span) => [
                        span,
                        if (filter != filters.last) const TextSpan(text: ", "),
                      ],
                    ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          GestureDetector(
            onTap: () {
              appliedAgent.value = null;
              appliedPropertyType.value = null;
              appliedStatus.value = null;
              selectedAgent.value = null;
              selectedPropertyType.value = null;
              selectedStatus.value = null;
              // Hide tooltip when close button is clicked
              showTooltip.value = false;
            },
            child: const Icon(Icons.close, size: 16, color: Colors.black),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterWidget(
    BuildContext context,
    ValueNotifier<String?> selectedAgent,
    ValueNotifier<String?> selectedPropertyType,
    ValueNotifier<String?> selectedStatus,
    ValueNotifier<bool> showFilter,
    ValueNotifier<int> currentPage,
    ValueNotifier<bool> showTooltip,
    ValueNotifier<String?> appliedAgent,
    ValueNotifier<String?> appliedPropertyType,
    ValueNotifier<String?> appliedStatus,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(color: AppTheme.filterBgColor),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(filterBy, style: AppFonts.semiBoldTextStyle(18)),
              GestureDetector(
                onTap: () {
                  showFilter.value = false;
                },
                child: const Icon(Icons.close, size: 20),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Make filter fields responsive
          Responsive.isMobile(context)
            ? Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildDropdown(
                          'Select Agent',
                          selectedAgent.value,
                          ['Mike Benson', 'Laura Chen', 'Kelly Group LLC', 'David Rivera', 'Alice Nguyen'],
                          (value) => selectedAgent.value = value,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildDropdown(
                          'Select Property Type',
                          selectedPropertyType.value,
                          ['Traditional', 'Lease', 'Commercial'],
                          (value) => selectedPropertyType.value = value,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildDropdown(
                          'Select Status',
                          selectedStatus.value,
                          ['Completed', 'Pending', 'Cancelled'],
                          (value) => selectedStatus.value = value,
                        ),
                      ),
                      const SizedBox(width: 12),
                      SizedBox(
                        width: 100,
                        height: 40,
                        child: ElevatedButton(
                          onPressed: () {
                            // Copy selected values to applied values
                            appliedAgent.value = selectedAgent.value;
                            appliedPropertyType.value = selectedPropertyType.value;
                            appliedStatus.value = selectedStatus.value;

                            currentPage.value = 1;
                            // Show tooltip only when Apply button is clicked
                            showTooltip.value = true;
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.roundIconColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                          child: const Text(apply),
                        ),
                      ),
                    ],
                  ),
                ],
              )
            : Responsive.isTablet(context)
              ? Wrap(
                  spacing: 12,
                  runSpacing: 12,
                  children: [
                    SizedBox(
                      width: (MediaQuery.of(context).size.width - 100) / 4,
                      child: _buildDropdown(
                        'Select Agent',
                        selectedAgent.value,
                        ['Mike Benson', 'Laura Chen', 'Kelly Group LLC', 'David Rivera', 'Alice Nguyen'],
                        (value) => selectedAgent.value = value,
                      ),
                    ),
                    SizedBox(
                      width: (MediaQuery.of(context).size.width - 100) / 4,
                      child: _buildDropdown(
                        'Select Property Type',
                        selectedPropertyType.value,
                        ['Traditional', 'Lease', 'Commercial'],
                        (value) => selectedPropertyType.value = value,
                      ),
                    ),
                    SizedBox(
                      width: (MediaQuery.of(context).size.width - 100) / 4,
                      child: _buildDropdown(
                        'Select Status',
                        selectedStatus.value,
                        ['Completed', 'Pending', 'Cancelled'],
                        (value) => selectedStatus.value = value,
                      ),
                    ),
                    SizedBox(
                      width: 120,
                      height: 40,
                      child: ElevatedButton(
                        onPressed: () {
                          // Copy selected values to applied values
                          appliedAgent.value = selectedAgent.value;
                          appliedPropertyType.value = selectedPropertyType.value;
                          appliedStatus.value = selectedStatus.value;

                          currentPage.value = 1;
                          // Show tooltip only when Apply button is clicked
                          showTooltip.value = true;
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.roundIconColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ),
                        child: const Text(apply),
                      ),
                    ),
                  ],
                )
              : Row(
                  children: [
                    SizedBox(
                      width: ResponsiveSizes.comboBoxWidth(context),
                      child: _buildDropdown(
                        'Select Agent',
                        selectedAgent.value,
                        ['Mike Benson', 'Laura Chen', 'Kelly Group LLC', 'David Rivera', 'Alice Nguyen'],
                        (value) => selectedAgent.value = value,
                      ),
                    ),
                    const SizedBox(width: 12),
                    SizedBox(
                      width: ResponsiveSizes.comboBoxWidth(context),
                      child: _buildDropdown(
                        'Select Property Type',
                        selectedPropertyType.value,
                        ['Traditional', 'Lease', 'Commercial'],
                        (value) => selectedPropertyType.value = value,
                      ),
                    ),
                    const SizedBox(width: 12),
                    SizedBox(
                      width: ResponsiveSizes.comboBoxWidth(context),
                      child: _buildDropdown(
                        'Select Status',
                        selectedStatus.value,
                        ['Completed', 'Pending', 'Cancelled'],
                        (value) => selectedStatus.value = value,
                      ),
                    ),
                    const SizedBox(width: 12),
                    SizedBox(
                      width: ResponsiveSizes.applyButtonWidth(context),
                      height: 40,
                      child: ElevatedButton(
                        onPressed: () {
                          // Copy selected values to applied values
                          appliedAgent.value = selectedAgent.value;
                          appliedPropertyType.value = selectedPropertyType.value;
                          appliedStatus.value = selectedStatus.value;

                          currentPage.value = 1;
                          // Show tooltip only when Apply button is clicked
                          showTooltip.value = true;
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.roundIconColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ),
                        child: const Text(apply),
                      ),
                    ),
                  ],
                ),
        ],
      ),
    );
  }

  Widget _buildDropdown(
    String hint,
    String? value,
    List<String> items,
    Function(String?) onChanged,
  ) {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          // Add conditional border color
          color: value != null
              ? AppTheme.selectedComboBoxBorder
              : AppTheme.comboBoxBorder,
          width: 1.0,
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: ButtonTheme(
          alignedDropdown: true,
          child: DropdownButton<String>(
            value: value,
            hint: Padding(
              padding: const EdgeInsets.only(left: 12),
              child: Text(
                hint,
                style: AppFonts.regularTextStyle(14, color: AppTheme.black),
              ),
            ),
            isExpanded: true,
            dropdownColor: Colors.white,
            menuMaxHeight: 300,
            // Add these properties to control dropdown position and style
            borderRadius: BorderRadius.circular(20),
            // Force dropdown to appear below
            items: items.map((item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Padding(
                  padding: const EdgeInsets.only(left: 12),
                  child: Text(item, style: AppFonts.regularTextStyle(14)),
                ),
              );
            }).toList(),
            onChanged: onChanged,
          ),
        ),
      ),
    );
  }

  Widget _buildTable(
    BuildContext context,
    BoxConstraints constraints,
    List<Sales> sales,
  ) {
    if (Responsive.isMobile(context)) {
      return _buildMobileTable(context, sales);
    } else if (Responsive.isTablet(context)) {
      return _buildTabletTable(context, constraints, sales);
    } else {
      return _buildDesktopTable(context, constraints, sales);
    }
  }

  Widget _buildDesktopTable(
    BuildContext context,
    BoxConstraints constraints,
    List<Sales> sales,
  ) {
    final ScrollController horizontalScrollController = ScrollController();

    final bool enableScroll = constraints.maxWidth <= 1400;

    Widget tableContent = SizedBox(
      width: math.max(constraints.maxWidth - (defaultPadding * 2), 1400),
      child: DataTable(
        columnSpacing: defaultPadding * 0.8,
        dataRowMinHeight: 40,
        dataRowMaxHeight: 50,
        horizontalMargin: 0,
        checkboxHorizontalMargin: 0,
        columns: [
          _dataColumn(name: 'Transaction ID'),
          _dataColumn(name: 'Agent Name'),
          _dataColumn(name: 'Property Type'),
          _dataColumn(name: 'Property Address'),
          _dataColumn(name: 'Property Value'),
          _dataColumn(name: 'Buyer Name'),
          _dataColumn(name: 'Buyer Address'),
          _dataColumn(name: 'Listing Date'),
          _dataColumn(name: 'Sale Date'),
          _dataColumn(name: 'Sale Price'),
          _dataColumn(name: 'Commission'),
          _dataColumn(name: 'Commission Amt'),
          _dataColumn(name: actions, allowSort: false),
        ],
        rows: sales
            .map((sale) => _salesDesktopDataRow(context, sale))
            .toList(),
      ),
    );

    if (enableScroll) {
      return Scrollbar(
        controller: horizontalScrollController,
        thumbVisibility: true,
        child: SingleChildScrollView(
          controller: horizontalScrollController,
          scrollDirection: Axis.horizontal,
          child: tableContent,
        ),
      );
    } else {
      return tableContent;
    }
  }

  DataColumn _dataColumn({
    String name = '',
    bool allowSort = true,
    double fontSize = 14,
  }) {
    return DataColumn(
      label: Expanded(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Flexible(
              child: Text(
                name,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
                style: AppFonts.regularTextStyle(
                  fontSize,
                  color: AppTheme.tableHeaderFont,
                ),
              ),
            ),
            if (allowSort && name != actions) ...[
              const SizedBox(width: 4),
              Image.asset(
                '$iconAssetpath/column_sort.png',
                height: 16,
                width: 16,
                color: AppTheme.tableHeaderFont,
              ),
            ],
          ],
        ),
      ),
    );
  }

  DataRow _salesDesktopDataRow(BuildContext context, Sales sale) {
    return DataRow(
      cells: [
        DataCell(
          Text(
            sale.transactionId,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            sale.agentName,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            sale.propertyType,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            sale.propertyAddress,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            sale.propertyValue,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            sale.buyerName,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            sale.buyerAddress,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            sale.listingDate,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            sale.saleDate,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            sale.salePrice,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            sale.commission,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            sale.commissionAmount,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          ActionButtonEye(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => SalesReviewDocScreen(enableEditing: false),
                ),
              );
            },
            isCompact: false,
            isMobile: false,
          ),
        ),
      ],
    );
  }

  Widget _buildTabletTable(
    BuildContext context,
    BoxConstraints constraints,
    List<Sales> sales,
  ) {
    final ScrollController horizontalScrollController = ScrollController();

    return Scrollbar(
      controller: horizontalScrollController,
      thumbVisibility: true,
      child: SingleChildScrollView(
        controller: horizontalScrollController,
        scrollDirection: Axis.horizontal,
        child: SizedBox(
          width: math.max(constraints.maxWidth - (defaultPadding * 2), 1000),
          child: DataTable(
            columnSpacing: defaultPadding * 0.6,
            dataRowMinHeight: 48,
            dataRowMaxHeight: 52,
            horizontalMargin: 0,
            checkboxHorizontalMargin: 0,
            columns: [
              _dataColumn(name: 'Transaction ID', fontSize: 12),
              _dataColumn(name: 'Agent Name', fontSize: 12),
              _dataColumn(name: 'Property Type', fontSize: 12),
              _dataColumn(name: 'Buyer Name', fontSize: 12),
              _dataColumn(name: 'Sale Price', fontSize: 12),
              _dataColumn(name: 'Commission', fontSize: 12),
              _dataColumn(name: 'Commission Amt', fontSize: 12),
              _dataColumn(name: actions, fontSize: 12, allowSort: false),
            ],
            rows: sales
                .map((sale) => _salesTabletDataRow(context, sale))
                .toList(),
          ),
        ),
      ),
    );
  }

  DataRow _salesTabletDataRow(BuildContext context, Sales sale) {
    return DataRow(
      cells: [
        DataCell(
          Text(
            sale.transactionId,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(12, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            sale.agentName,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(12, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            sale.propertyType,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(12, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            sale.buyerName,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(12, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            sale.salePrice,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(12, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            sale.commission,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(12, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          Text(
            sale.commissionAmount,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.regularTextStyle(12, color: AppTheme.tableDataFont),
          ),
        ),
        DataCell(
          ActionButtonEye(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => SalesReviewDocScreen(enableEditing: false),
                ),
              );
            },
            isCompact: true,
            isMobile: false,
          ),
        ),
      ],
    );
  }

  Widget _buildMobileTable(BuildContext context, List<Sales> sales) {
    return Column(
      children: sales
          .map((sale) => _buildMobileCard(context, sale))
          .toList(),
    );
  }

  Widget _buildMobileCard(BuildContext context, Sales sale) {
    return Container(
      margin: const EdgeInsets.only(bottom: defaultPadding),
      padding: const EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            sale.transactionId,
            style: AppFonts.boldTextStyle(14, color: AppTheme.tableDataFont),
          ),
          const SizedBox(height: 12),
          _buildMobileCardRow('Agent', sale.agentName),
          _buildMobileCardRow('Property Type', sale.propertyType),
          _buildMobileCardRow('Buyer', sale.buyerName),
          _buildMobileCardRow('Sale Price', sale.salePrice),
          _buildMobileCardRow('Commission', sale.commission),
          _buildMobileCardRow('Commission Amount', sale.commissionAmount),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              ActionButtonEye(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => SalesReviewDocScreen(enableEditing: false),
                    ),
                  );
                },
                isCompact: true,
                isMobile: true,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMobileCardRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: AppFonts.regularTextStyle(12, color: Colors.grey)),
          Flexible(
            child: Text(
              value,
              style: AppFonts.regularTextStyle(
                12,
                color: AppTheme.tableDataFont,
              ),
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter(
    BuildContext context,
    ValueNotifier<int> currentPage,
    ValueNotifier<int> itemsPerPage,
    List<Sales> filteredSales,
    int totalPages,
  ) {
    if (Responsive.isMobile(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _showingDataText(
            context,
            currentPage.value,
            itemsPerPage.value,
            filteredSales,
          ),
          const SizedBox(height: defaultPadding),
          _buildPagination(context, currentPage, itemsPerPage, totalPages),
        ],
      );
    }

    return Row(
      children: [
        _showingDataText(
          context,
          currentPage.value,
          itemsPerPage.value,
          filteredSales,
        ),
        const Spacer(),
        _buildPagination(context, currentPage, itemsPerPage, totalPages),
      ],
    );
  }

  Widget _showingDataText(
    BuildContext context,
    int currentPage,
    int itemsPerPage,
    List<Sales> filteredSales,
  ) {
    final startIndex = (currentPage - 1) * itemsPerPage + 1;
    final endIndex = (currentPage * itemsPerPage).clamp(
      0,
      filteredSales.length,
    );

    return Text(
      '$showingDataLabelP1 $startIndex $toLabel $endIndex $ofLabel ${filteredSales.length} entries',
      style: Theme.of(context).textTheme.bodySmall,
    );
  }

  Widget _buildPagination(
    BuildContext context,
    ValueNotifier<int> currentPage,
    ValueNotifier<int> itemsPerPage,
    int totalPages,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _paginationButton(
          icon: Icons.chevron_left,
          onPressed: currentPage.value > 1 ? () => currentPage.value-- : null,
        ),
        ...List.generate(math.min(5, totalPages), (index) {
          final pageNum = index + 1;
          return _paginationButton(
            label: pageNum.toString(),
            isSelected: pageNum == currentPage.value,
            onPressed: () => currentPage.value = pageNum,
          );
        }),
        if (totalPages > 5) ...[
          _paginationButton(label: '...'),
          _paginationButton(
            label: totalPages.toString(),
            onPressed: () => currentPage.value = totalPages,
          ),
        ],
        _paginationButton(
          icon: Icons.chevron_right,
          onPressed: currentPage.value < totalPages
              ? () => currentPage.value++
              : null,
        ),
      ],
    );
  }

  Widget _paginationButton({
    String? label,
    IconData? icon,
    bool isSelected = false,
    VoidCallback? onPressed,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 2),
      child: Material(
        color: isSelected ? AppTheme.paginationActiveBg : Colors.transparent,
        borderRadius: BorderRadius.circular(5),
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(5),
          child: Container(
            width: 30,
            height: 30,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Center(
              child: icon != null
                  ? Icon(
                      icon,
                      size: 16,
                      color: isSelected ? Colors.white : Colors.black,
                    )
                  : Text(
                      label!,
                      style: AppFonts.regularTextStyle(
                        12,
                        color: isSelected ? Colors.white : Colors.black,
                      ),
                    ),
            ),
          ),
        ),
      ),
    );
  }
}