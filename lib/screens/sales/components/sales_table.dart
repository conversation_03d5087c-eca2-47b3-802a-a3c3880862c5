import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../../theme/app_theme.dart';

class SalesTableData {
  final String transactionId;
  final String agentName;
  final String propertyType;
  final String propertyAddress;
  final String propertyValue;
  final String buyerName;
  final String buyerAddress;
  final String listingDate;
  final String saleDate;
  final String salePrice;
  final String commission;
  final String commissionAmount;

  SalesTableData({
    required this.transactionId,
    required this.agentName,
    required this.propertyType,
    required this.propertyAddress,
    required this.propertyValue,
    required this.buyerName,
    required this.buyerAddress,
    required this.listingDate,
    required this.saleDate,
    required this.salePrice,
    required this.commission,
    required this.commissionAmount,
  });
}

class SalesTable extends HookWidget {
  const SalesTable({super.key});

  @override
  Widget build(BuildContext context) {
    // Sample data - replace with your actual data source
    final salesData = useState<List<SalesTableData>>([
      SalesTableData(
        transactionId: 'TXN-2025-0701-01',
        agentName: '<PERSON>',
        propertyType: 'Traditional',
        propertyAddress: '245 Park St, Phoenix, AZ',
        propertyValue: '\$850,000',
        buyerName: '<PERSON>',
        buyerAddress: '456 Oak Dr, Phoenix, AZ',
        listingDate: '04/10/2025',
        saleDate: '07/01/2025',
        salePrice: '\$495,000',
        commission: '4%',
        commissionAmount: '\$18,000',
      ),
      SalesTableData(
        transactionId: 'TXN-2025-0701-02',
        agentName: 'Laura Chen',
        propertyType: 'Lease',
        propertyAddress: '245 Park Ave, Austin, TX',
        propertyValue: '\$850,000',
        buyerName: 'Sarah Miles',
        buyerAddress: '321 Sunset Ln, Miami, FL',
        listingDate: '04/10/2025',
        saleDate: '07/01/2025',
        salePrice: '\$640,000',
        commission: '4%',
        commissionAmount: '\$18,000',
      ),
      SalesTableData(
        transactionId: 'TXN-2025-0701-03',
        agentName: 'Kelly Group LLC',
        propertyType: 'Commercial',
        propertyAddress: '101 Main St, Phoenix, AZ',
        propertyValue: '\$1,200,000',
        buyerName: 'William Rose',
        buyerAddress: '88 Market St, Austin, TX',
        listingDate: '05/20/2025',
        saleDate: '07/01/2025',
        salePrice: '\$1,180,000',
        commission: '3%',
        commissionAmount: '\$47,200',
      ),
      SalesTableData(
        transactionId: 'TXN-2025-0701-04',
        agentName: 'David Rivera',
        propertyType: 'Traditional',
        propertyAddress: '245 Park St, Austin, TX',
        propertyValue: '\$420,000',
        buyerName: 'Emma Brooks',
        buyerAddress: '102 Pine St, Denver, CO',
        listingDate: '06/15/2025',
        saleDate: '07/01/2025',
        salePrice: '\$418,000',
        commission: '4%',
        commissionAmount: '\$12,540',
      ),
      SalesTableData(
        transactionId: 'TXN-2025-0701-05',
        agentName: 'Alice Nguyen',
        propertyType: 'Lease',
        propertyAddress: '245 Park Ave, Phoenix, AZ',
        propertyValue: '\$12,000,000',
        buyerName: 'Tom White',
        buyerAddress: '5 Maple Ave, Seattle, WA',
        listingDate: '04/20/2025',
        saleDate: '07/01/2025',
        salePrice: '\$3,000',
        commission: '3%',
        commissionAmount: '\$1,500',
      ),
    ]);

    final currentPage = useState(1);
    final itemsPerPage = 10;
    final totalItems = salesData.value.length;
    final totalPages = (totalItems / itemsPerPage).ceil();

    return Container(
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: AppTheme.borderColor,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.person,
                      color: AppTheme.primaryColor,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Sales',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.primaryTextColor,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: AppTheme.searchbarBg,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: const Row(
                        children: [
                          Icon(
                            Icons.filter_list,
                            size: 16,
                            color: AppTheme.secondaryTextColor,
                          ),
                          SizedBox(width: 4),
                          Text(
                            'Filter',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppTheme.secondaryTextColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 12),
                    Container(
                      width: 200,
                      height: 36,
                      decoration: BoxDecoration(
                        color: AppTheme.searchbarBg,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: const TextField(
                        decoration: InputDecoration(
                          hintText: 'Search',
                          hintStyle: TextStyle(
                            color: AppTheme.secondaryTextColor,
                            fontSize: 14,
                          ),
                          prefixIcon: Icon(
                            Icons.search,
                            color: AppTheme.secondaryTextColor,
                            size: 18,
                          ),
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(vertical: 8),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Table
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Container(
                width: 1400, // Fixed width to accommodate all columns
                child: DataTable(
                  headingRowHeight: 50,
                  dataRowHeight: 60,
                  headingTextStyle: const TextStyle(
                    color: AppTheme.tableColumnHeaderColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  dataTextStyle: const TextStyle(
                    color: AppTheme.primaryTextColor,
                    fontSize: 12,
                  ),
                  columns: const [
                    DataColumn(
                      label: Text('Transaction ID'),
                    ),
                    DataColumn(
                      label: Text('Agent Name'),
                    ),
                    DataColumn(
                      label: Text('Property Type'),
                    ),
                    DataColumn(
                      label: Text('Property Address'),
                    ),
                    DataColumn(
                      label: Text('Property Value'),
                    ),
                    DataColumn(
                      label: Text('Buyer Name'),
                    ),
                    DataColumn(
                      label: Text('Buyer Address'),
                    ),
                    DataColumn(
                      label: Text('Listing Date'),
                    ),
                    DataColumn(
                      label: Text('Sale Date'),
                    ),
                    DataColumn(
                      label: Text('Sale Price'),
                    ),
                    DataColumn(
                      label: Text('Commission'),
                    ),
                    DataColumn(
                      label: Text('Commission Amt'),
                    ),
                    DataColumn(
                      label: Text('Actions'),
                    ),
                  ],
                  rows: salesData.value.map((data) {
                    return DataRow(
                      cells: [
                        DataCell(Text(data.transactionId)),
                        DataCell(Text(data.agentName)),
                        DataCell(
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getPropertyTypeColor(data.propertyType),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              data.propertyType,
                              style: const TextStyle(
                                color: AppTheme.white,
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                        DataCell(
                          SizedBox(
                            width: 120,
                            child: Text(
                              data.propertyAddress,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 2,
                            ),
                          ),
                        ),
                        DataCell(Text(data.propertyValue)),
                        DataCell(Text(data.buyerName)),
                        DataCell(
                          SizedBox(
                            width: 120,
                            child: Text(
                              data.buyerAddress,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 2,
                            ),
                          ),
                        ),
                        DataCell(Text(data.listingDate)),
                        DataCell(Text(data.saleDate)),
                        DataCell(Text(data.salePrice)),
                        DataCell(Text(data.commission)),
                        DataCell(Text(data.commissionAmount)),
                        DataCell(
                          Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: AppTheme.primaryColor,
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: const Icon(
                              Icons.visibility,
                              color: AppTheme.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ],
                    );
                  }).toList(),
                ),
              ),
            ),
          ),

          // Pagination
          Container(
            padding: const EdgeInsets.all(20),
            decoration: const BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: AppTheme.borderColor,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Showing data 1 to 10 of $totalItems entries',
                  style: const TextStyle(
                    color: AppTheme.pageSummaryLabelColor,
                    fontSize: 14,
                  ),
                ),
                Row(
                  children: [
                    for (int i = 1; i <= totalPages && i <= 5; i++)
                      Container(
                        margin: const EdgeInsets.only(right: 8),
                        child: InkWell(
                          onTap: () => currentPage.value = i,
                          child: Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: currentPage.value == i
                                  ? AppTheme.paginationActiveBg
                                  : AppTheme.paginationInactiveBg,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Center(
                              child: Text(
                                i.toString(),
                                style: TextStyle(
                                  color: currentPage.value == i
                                      ? AppTheme.white
                                      : AppTheme.primaryTextColor,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    if (totalPages > 5) ...[
                      const Text('...'),
                      const SizedBox(width: 8),
                      InkWell(
                        onTap: () => currentPage.value = totalPages,
                        child: Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: currentPage.value == totalPages
                                ? AppTheme.paginationActiveBg
                                : AppTheme.paginationInactiveBg,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Center(
                            child: Text(
                              totalPages.toString(),
                              style: TextStyle(
                                color: currentPage.value == totalPages
                                    ? AppTheme.white
                                    : AppTheme.primaryTextColor,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                    const SizedBox(width: 16),
                    InkWell(
                      onTap: currentPage.value < totalPages
                          ? () => currentPage.value++
                          : null,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: AppTheme.paginationInactiveBg,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Row(
                          children: [
                            Text(
                              'Next',
                              style: TextStyle(
                                color: AppTheme.primaryTextColor,
                                fontSize: 14,
                              ),
                            ),
                            SizedBox(width: 4),
                            Icon(
                              Icons.arrow_forward_ios,
                              size: 12,
                              color: AppTheme.primaryTextColor,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getPropertyTypeColor(String propertyType) {
    switch (propertyType.toLowerCase()) {
      case 'traditional':
        return AppTheme.primaryColor;
      case 'lease':
        return AppTheme.commissionCardColor;
      case 'commercial':
        return AppTheme.secondaryColor;
      default:
        return AppTheme.primaryColor;
    }
  }
}