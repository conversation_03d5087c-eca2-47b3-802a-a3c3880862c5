import 'dart:math' as math;

import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../config/json_consts.dart';
import '../../../models/broker.dart';
import '../../../theme/app_theme.dart';
import '../../agent_network/agent_network_screen.dart';
import '/config/constants.dart';
import '/config/responsive.dart';
import '../../../theme/app_fonts.dart';
import '/screens/dashboard/components/rounderd_icon_btn.dart';

import '../../../config/app_strings.dart';

class BrokersTable extends HookWidget {
  BrokersTable({super.key});

  final ValueNotifier<bool> showFilterView = ValueNotifier<bool>(false);
  final ValueNotifier<bool> showFilterResults = ValueNotifier<bool>(false);
  final ValueNotifier<String> selectedDateRange = ValueNotifier<String>('');
  final ValueNotifier<bool> filterApplied = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    // State management using hooks
    final currentPage = useState<int>(1);
    final searchQuery = useState<String>('');
    final searchController = useTextEditingController();

    const int itemsPerPage = 10;

    // Setup search listener
    useEffect(() {
      void onSearchChanged() {
        searchQuery.value = searchController.text;
        currentPage.value = 1; // Reset to first page when searching
      }

      searchController.addListener(onSearchChanged);
      return () => searchController.removeListener(onSearchChanged);
    }, [searchController]);

    // Filter brokers based on search query
    List<Broker> getFilteredBrokers() {
      if (searchQuery.value.isEmpty) {
        return brokersListJson;
      }
      return brokersListJson
          .where(
            (broker) => broker.name.toLowerCase().contains(
              searchQuery.value.toLowerCase(),
            ),
          )
          .toList();
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          width: constraints.maxWidth,

          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(15),
            child: Column(
              children: [
                // Responsive.isMobile(context)
                //   ?Container():
                ValueListenableBuilder<bool>(
                  valueListenable: showFilterView,
                  builder: (context, showFilter, child) {
                    return showFilter
                        ? AnimatedContainer(
                            padding: EdgeInsets.symmetric(
                              vertical: defaultPadding,
                              horizontal: defaultPadding * 1.5,
                            ),
                            color: AppTheme.filterBgColor,
                            duration: const Duration(milliseconds: 300),
                            //height: showFilter ? 80 : 0,
                            child: _buildFilterView(context),
                          )
                        : SizedBox.shrink();
                  },
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                    vertical: defaultPadding,
                    horizontal: defaultPadding * 1.5,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ValueListenableBuilder(
                        valueListenable: showFilterResults,
                        builder: (context, value, child) {
                          return _buildHeader(
                            context,
                            searchController,
                            searchQuery,
                            currentPage,
                          );
                        },
                      ),
                      const SizedBox(height: defaultPadding),
                      _buildTable(
                        context,
                        constraints,
                        getFilteredBrokers(),
                        currentPage,
                        itemsPerPage,
                      ),

                      const SizedBox(height: defaultPadding),
                      _buildFooter(
                        context,
                        getFilteredBrokers(),
                        currentPage,
                        itemsPerPage,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(
    BuildContext context,
    TextEditingController searchController,
    ValueNotifier<String> searchQuery,
    ValueNotifier<int> currentPage,
  ) {
    if (Responsive.isMobile(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              RoundIconBtn(
                icon: 'user',
                backgroundColor: Colors.transparent,
                onPressed: () {},
                iconSize: 20,
              ),
              const SizedBox(width: 5),
              Expanded(
                child: Text(
                  brokersTitle,
                  style: AppFonts.semiBoldTextStyle(18),
                ),
              ),
              showFilterResults.value
                  ? _buildFilterResultsBanner(context)
                  : Container(),
            ],
          ),
          const SizedBox(height: defaultPadding),

          Row(
            children: [
              Container(width: 100, child: _buildFilterBtn()),
              const SizedBox(width: defaultPadding / 2),
              Expanded(
                child: TextField(
                  controller: searchController,
                  decoration: InputDecoration(
                    hintText: searchHint,
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(30),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.grey[100],
                    contentPadding: const EdgeInsets.symmetric(vertical: 0),
                  ),
                  onChanged: (value) {
                    searchQuery.value = value;
                    currentPage.value = 1; // Reset to first page when searching
                  },
                ),
              ),
            ],
          ),
        ],
      );
    }

    return Container(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset('$iconAssetpath/user.png', height: 20, width: 20),
          const SizedBox(width: 8),
          Container(
            child: Text(brokersTitle, style: AppFonts.semiBoldTextStyle(22)),
          ),
          const SizedBox(width: 5),
          showFilterResults.value
              ? _buildFilterResultsBanner(context)
              : Container(),
          const Spacer(),
          _buildFilterBtn(),
          const SizedBox(width: 16),
          SizedBox(
            width: Responsive.isTablet(context) ? 150 : 200,
            child: TextField(
              controller: searchController,
              decoration: InputDecoration(
                hintText: searchHint,
                hintStyle: AppFonts.regularTextStyle(
                  14,
                  color: AppTheme.primaryTextColor,
                ),
                prefixIcon: Container(
                  height: 10,
                  margin: const EdgeInsets.only(left: 8, top: 8, bottom: 8),
                  child: Image.asset(
                    '$iconAssetpath/search.png',
                    height: 10,
                    width: 10,
                  ),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(30),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: AppTheme.searchbarBg,
                contentPadding: const EdgeInsets.symmetric(vertical: 0),
              ),
              onChanged: (value) {
                searchQuery.value = value;
                currentPage.value = 1; // Reset to first page when searching
              },
            ),
          ),
        ],
      ),
    );
  }

  IconButton _buildFilterBtn() {
    return IconButton(
      icon: Row(
        children: [
          Image.asset('$iconAssetpath/filter.png', height: 16, width: 15),
          const SizedBox(width: 5),
          Text(
            'Filter',
            style: AppFonts.regularTextStyle(15, color: Colors.white),
          ),
        ],
      ),
      style: IconButton.styleFrom(
        backgroundColor: AppTheme.roundIconColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
      ),
      iconSize: 50,
      onPressed: () {
        print("Image Icon Button Clicked");
        showFilterView.value = !showFilterView.value;
      },
    );
  }

  Widget _buildTable(
    BuildContext context,
    BoxConstraints constraints,
    List<Broker> filteredBrokers,
    ValueNotifier<int> currentPage,
    int itemsPerPage,
  ) {
    // Get filtered brokers based on search
    final List<Broker> brokersToDisplay = filteredBrokers;

    // Calculate start and end indices for pagination
    final int startIndex = (currentPage.value - 1) * itemsPerPage;
    final int endIndex = math.min(
      startIndex + itemsPerPage,
      brokersToDisplay.length,
    );

    // Get the paginated brokers list
    final List<Broker> paginatedBrokers = brokersToDisplay.isEmpty
        ? []
        : brokersToDisplay.sublist(startIndex, endIndex);

    if (Responsive.isMobile(context)) {
      return _buildMobileTable(context, paginatedBrokers);
    } else if (Responsive.isTablet(context)) {
      return FittedBox(
        child: _buildTabletTable(context, constraints, paginatedBrokers),
      );
    } else {
      return FittedBox(
        child: _buildDesktopTable(context, constraints, paginatedBrokers),
      );
    }
  }

  Widget _buildDesktopTable(
    BuildContext context,
    BoxConstraints constraints,
    List<Broker> paginatedBrokers,
  ) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SizedBox(
        width: constraints.maxWidth,
        child: DataTable(
          columnSpacing: defaultPadding * 0.8,
          dataRowMinHeight: 40,
          dataRowMaxHeight: 50,
          horizontalMargin: 0,
          checkboxHorizontalMargin: 0,

          columns: [
            _dataColumn(name: brokerColumnHeader),
            _dataColumn(name: contactsColumnHeader),
            _dataColumn(name: emailAddressColumnHeader),
            _dataColumn(name: joinDateColumnHeader),
            _dataColumn(name: totalAgentsColumnHeader, allowSort: false),
            _dataColumn(name: totalSalesColumnHeader),
            _dataColumn(name: actionsColumnHeader, allowSort: false),
          ],
          rows: List.generate(
            paginatedBrokers.length,
            (index) => _brokerDesktopDataRow(context, paginatedBrokers[index]),
          ),
        ),
      ),
    );
  }

  DataColumn _dataColumn({
    String name = '',
    bool allowSort = true,
    double fontSize = 14,
  }) {
    return DataColumn(
      label: Expanded(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            if (name != '')
              Flexible(
                child: Text(
                  name,
                  maxLines: 2,
                  style: AppFonts.mediumTextStyle(
                    fontSize,
                    color: AppTheme.tableColumnHeaderColor,
                  ),
                ),
              ),
            if (allowSort)
              Image.asset(
                '$iconAssetpath/column_sort.png',
                height: 16,
                width: 16,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabletTable(
    BuildContext context,
    BoxConstraints constraints,
    List<Broker> paginatedBrokers,
  ) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Container(
        width: constraints.maxWidth,
        child: DataTable(
          columnSpacing: defaultPadding * 0.4,
          dataRowMinHeight: 48,
          dataRowMaxHeight: 52,
          horizontalMargin: 0,
          checkboxHorizontalMargin: 0,
          columns: [
            _dataColumn(name: brokerColumnHeader, fontSize: 12),
            _dataColumn(name: contactsColumnHeader, fontSize: 12),
            _dataColumn(name: emailAddressColumnHeader, fontSize: 12),
            _dataColumn(name: joinDateColumnHeader, fontSize: 12),
            _dataColumn(
              name: totalAgentsColumnHeader,
              fontSize: 12,
              allowSort: false,
            ),
            _dataColumn(name: totalSalesColumnHeader, fontSize: 12),
            _dataColumn(
              name: '      $actionsColumnHeader      ',
              fontSize: 12,
              allowSort: false,
            ),
          ],
          rows: List.generate(
            paginatedBrokers.length,
            (index) => _brokerDataRowTablet(
              context,
              paginatedBrokers[index],
              constraints,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileTable(
    BuildContext context,
    List<Broker> paginatedBrokers,
  ) {
    return Column(
      children: List.generate(
        paginatedBrokers.length,
        (index) => _buildMobileCard(context, paginatedBrokers[index]),
      ),
    );
  }

  Widget _buildMobileCard(BuildContext context, Broker broker) {
    String formattedJoinDate = DateFormat('MM/dd/yyyy').format(broker.joinDate);
    return Container(
      margin: const EdgeInsets.only(bottom: defaultPadding),
      padding: const EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _BrokerNameCell(
            name: broker.name,
            onViewPressed: () {},
            isMobile: true,
          ),
          const SizedBox(height: 12),
          _buildMobileCardRow(contactsColumnHeader, broker.contact),
          _buildMobileCardRow(emailAddressColumnHeader, broker.email),
          _buildMobileCardRow(
            joinDateColumnHeader,
            formattedJoinDate,
          ), // Add join date
          _buildMobileCardRow(
            totalAgentsColumnHeader,
            broker.agents.length.toString(),
          ),
          _buildMobileCardRow(totalSalesColumnHeader, broker.sales.toString()),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ActionButtonEye(
              onPressed: () async => await _showAgentNetwork(context, broker),
              isMobile: true,
            ),
          ),
        ],
      ),
    );
  }

  DataRow _brokerDesktopDataRow(BuildContext context, Broker broker) {
    String formattedJoinDate = DateFormat('MM/dd/yyyy').format(broker.joinDate);
    return DataRow(
      cells: [
        DataCell(
          _BrokerNameCell(
            name: broker.name,
            onViewPressed: () {},
            isCompact: false,
            isMobile: false,
          ),
        ),
        DataCell(Text(broker.contact, overflow: TextOverflow.ellipsis)),
        DataCell(Text(broker.email, overflow: TextOverflow.ellipsis)),
        DataCell(
          Text(formattedJoinDate, overflow: TextOverflow.ellipsis),
        ), // Add join date
        DataCell(
          Text(
            broker.agents.length.toString(),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        DataCell(
          Text(broker.sales.toString(), overflow: TextOverflow.ellipsis),
        ),
        DataCell(
          ActionButtonEye(
            onPressed: () async => await _showAgentNetwork(context, broker),
            isCompact: true,
            isMobile: false,
          ),
        ),
      ],
    );
  }

  DataRow _brokerDataRowTablet(
    BuildContext context,
    Broker broker,
    BoxConstraints constraints,
  ) {
    String formattedJoinDate = DateFormat('MM/dd/yyyy').format(broker.joinDate);
    return DataRow(
      cells: [
        DataCell(
          _BrokerNameCell(
            name: broker.name,
            onViewPressed: () {},
            isCompact: true,
            isMobile: false,
          ),
        ),
        _dataCell(broker.contact, TextOverflow.ellipsis, fontSize: 12),
        _dataCell(broker.email, TextOverflow.ellipsis, fontSize: 12),
        _dataCell(
          formattedJoinDate,
          TextOverflow.ellipsis,
          fontSize: 12,
        ), // Add join date
        _dataCell(
          broker.agents.length.toString(),
          TextOverflow.ellipsis,
          fontSize: 12,
        ),
        _dataCell(broker.sales.toString(), TextOverflow.ellipsis, fontSize: 12),
        DataCell(
          ActionButtonEye(
            onPressed: () async => await _showAgentNetwork(context, broker),
            isCompact: true,
            isMobile: false,
          ),
        ),
      ],
    );
  }

  DataCell _dataCell(
    String value,
    TextOverflow overflow, {
    double fontSize = 14,
  }) => DataCell(
    Text(
      value,
      overflow: overflow,
      style: AppFonts.mediumTextStyle(
        fontSize,
        color: AppTheme.primaryTextColor,
      ),
    ),
  );

  Widget _buildFooter(
    BuildContext context,
    List<Broker> filteredBrokers,
    ValueNotifier<int> currentPage,
    int itemsPerPage,
  ) {
    if (Responsive.isMobile(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _showingDatatext(context, filteredBrokers, currentPage, itemsPerPage),
          const SizedBox(height: defaultPadding),
          _buildPagination(context, filteredBrokers, currentPage, itemsPerPage),
        ],
      );
    }

    return Row(
      children: [
        _showingDatatext(context, filteredBrokers, currentPage, itemsPerPage),
        const Spacer(),
        _buildPagination(context, filteredBrokers, currentPage, itemsPerPage),
      ],
    );
  }

  Text _showingDatatext(
    BuildContext context,
    List<Broker> filteredBrokers,
    ValueNotifier<int> currentPage,
    int itemsPerPage,
  ) {
    final List<Broker> brokersToDisplay = filteredBrokers;
    final int startIndex = brokersToDisplay.isEmpty
        ? 0
        : (currentPage.value - 1) * itemsPerPage + 1;
    final int endIndex = math.min(
      currentPage.value * itemsPerPage,
      brokersToDisplay.length,
    );

    return Text(
      "$showingDataLabelP1 $startIndex $toLabel $endIndex $ofLabel ${brokersToDisplay.length} $showingDataLabelP2",
      style: Theme.of(context).textTheme.bodySmall,
    );
  }

  Widget _buildPagination(
    BuildContext context,
    List<Broker> filteredBrokers,
    ValueNotifier<int> currentPage,
    int itemsPerPage,
  ) {
    // Calculate total pages based on broker length
    final int totalPages = (filteredBrokers.length / itemsPerPage).ceil();

    // Create pagination buttons based on current page and total pages
    List<Widget> paginationButtons = [];

    // Add left arrow
    paginationButtons.add(
      _paginationButton(
        icon: Icons.chevron_left,
        onTap: currentPage.value > 1 ? () => currentPage.value-- : null,
      ),
    );

    // Add page numbers
    if (Responsive.isMobile(context) || totalPages <= 5) {
      // For mobile or few pages, show all pages or just 3
      final int pagesToShow = Responsive.isMobile(context) ? 3 : totalPages;
      for (int i = 1; i <= pagesToShow; i++) {
        paginationButtons.add(
          _paginationButton(
            label: "$i",
            isSelected: i == currentPage.value,
            onTap: () => currentPage.value = i,
          ),
        );
      }
    } else {
      // For desktop with many pages, show current page with neighbors and ellipsis
      // Always show first page
      paginationButtons.add(
        _paginationButton(
          label: "1",
          isSelected: 1 == currentPage.value,
          onTap: () => currentPage.value = 1,
        ),
      );

      // Show ellipsis if current page is not near the beginning
      if (currentPage.value > 3) {
        paginationButtons.add(_paginationButton(label: "..."));
      }

      // Show pages around current page
      for (
        int i = math.max(2, currentPage.value - 1);
        i <= math.min(totalPages - 1, currentPage.value + 1);
        i++
      ) {
        paginationButtons.add(
          _paginationButton(
            label: "$i",
            isSelected: i == currentPage.value,
            onTap: () => currentPage.value = i,
          ),
        );
      }

      // Show ellipsis if current page is not near the end
      if (currentPage.value < totalPages - 2) {
        paginationButtons.add(_paginationButton(label: "..."));
      }

      // Always show last page
      if (totalPages > 1) {
        paginationButtons.add(
          _paginationButton(
            label: "$totalPages",
            isSelected: totalPages == currentPage.value,
            onTap: () => currentPage.value = totalPages,
          ),
        );
      }
    }

    // Add right arrow
    paginationButtons.add(
      _paginationButton(
        icon: Icons.chevron_right,
        onTap: currentPage.value < totalPages
            ? () => currentPage.value++
            : null,
      ),
    );

    return Row(
      mainAxisAlignment: Responsive.isMobile(context)
          ? MainAxisAlignment.center
          : MainAxisAlignment.start,
      children: paginationButtons,
    );
  }

  Widget _paginationButton({
    String? label,
    IconData? icon,
    bool isSelected = false,
    VoidCallback? onTap,
  }) {
    final bool isDisabled = onTap == null;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 5),
        width: 30,
        height: 30,
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(5),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Center(
          child: icon != null
              ? Icon(
                  icon,
                  size: 16,
                  color: isDisabled
                      ? Colors.grey.shade400
                      : (isSelected ? Colors.white : Colors.black),
                )
              : Text(
                  label!,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.black,
                    fontSize: 12,
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildMobileCardRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
          Flexible(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  _showAgentNetwork(BuildContext context, Broker broker) async {
    // TODO: loader, api call
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AgentNetworkScreen(selectedBroker: broker),
      ),
    );
  }

  Widget _buildFilterView(BuildContext context) {
    return Container(
      //  padding: const EdgeInsets.symmetric(vertical: 16),
      // decoration: BoxDecoration(
      //   color: Colors.grey[50],
      //   borderRadius: BorderRadius.circular(8),
      //   border: Border.all(color: Colors.grey[300]!),
      // ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Filter by', style: AppFonts.mediumTextStyle(18)),

              IconButton(
                onPressed: () {
                  showFilterView.value = false;
                },
                icon: const Icon(Icons.close, size: 20),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  padding: const EdgeInsets.all(8),
                ),
              ),
            ],
          ),
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 40,
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[400]!),
                    borderRadius: BorderRadius.circular(20),
                    color: Colors.white,
                  ),
                  child: Row(
                    children: [
                      Text(
                        'Joining Date',
                        style: AppFonts.regularTextStyle(14),
                      ),
                      const Spacer(),
                      Icon(
                        Icons.calendar_today,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: ElevatedButton(
                    onPressed: () {
                      // Apply filter logic here
                      showFilterResults.value = true;
                      selectedDateRange.value =
                          'Jan 2024 - Dec 2024'; // Example date range
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.roundIconColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                    child: Text(
                      'Apply',
                      style: AppFonts.mediumTextStyle(14, color: Colors.white),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterResultsBanner(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      height: 20,
      decoration: BoxDecoration(
        color: AppTheme.filterResultBannerColor,
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: AppTheme.filterResultBannerBorderColor,
          width: 0.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          RichText(
            text: TextSpan(
              text: 'Showing Results for selected',
              style: AppFonts.regularTextStyle(10, color: AppTheme.black),
              children: [
                TextSpan(
                  text: ' Date',
                  style: AppFonts.mediumTextStyle(11, color: AppTheme.black),
                ),
              ],
            ),
          ),
          SizedBox(width: 5),
          Container(
            width: 10,
            child: IconButton(
              padding: EdgeInsets.zero,
              alignment: Alignment.center,
              onPressed: () {
                showFilterResults.value = false;
                selectedDateRange.value = '';
              },
              icon: const Icon(Icons.close, size: 9),
              style: IconButton.styleFrom(backgroundColor: Colors.transparent),
            ),
          ),
        ],
      ),
    );
  }
}

// Reusable Components
class _BrokerNameCell extends StatelessWidget {
  final String name;
  final VoidCallback onViewPressed;
  final bool isCompact;
  final bool isMobile;

  const _BrokerNameCell({
    required this.name,
    required this.onViewPressed,
    this.isCompact = false,
    this.isMobile = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isMobile) {
      return Container(
        child: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: const BoxDecoration(
                color: Colors.blue,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.person, color: Colors.white, size: 16),
            ),
            const SizedBox(width: 12),
            Expanded(child: Text('name', style: AppFonts.boldTextStyle(14))),
          ],
        ),
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Image.asset(
          '$iconAssetpath/agent_round.png',
          height: isCompact ? 20 : 24,
          width: isCompact ? 20 : 24,
        ),
        SizedBox(width: isCompact ? 4 : 8),
        Flexible(
          child: Text(
            name,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.mediumTextStyle(
              isCompact ? 12 : 14,
              color: AppTheme.primaryTextColor,
            ),
          ),
        ),
      ],
    );
  }
}

class ActionButtonEye extends StatelessWidget {
  final VoidCallback onPressed;
  final bool isCompact;
  final bool isMobile;

  const ActionButtonEye({
    required this.onPressed,
    this.isCompact = false,
    this.isMobile = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isMobile) {
      return TextButton.icon(
        onPressed: onPressed,
        icon: Image.asset(
          '$iconAssetpath/eye.png',
          height: isCompact ? 20 : 24,
          width: isCompact ? 20 : 24,
        ),
        label: Text(
          viewAgents,
          style: AppFonts.regularTextStyle(14, color: AppTheme.blueCardColor),
        ),
      );
    }

    final iconSize = isCompact ? 12.0 : 16.0;
    final alignment = isCompact ? Alignment.centerRight : Alignment.center;
    final maxWidth = isCompact ? 60.0 : double.infinity;

    return Container(
      constraints: BoxConstraints(maxWidth: maxWidth),
      child: Align(
        alignment: alignment,
        child: IconButton(
          onPressed: onPressed,
          highlightColor: AppTheme.roundIconBgColor,
          hoverColor: AppTheme.roundIconBgColor,
          alignment: Alignment.centerRight,
          tooltip: viewAgents,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(minWidth: 28, minHeight: 28),
          icon: Container(
            height: 28,
            width: 28,
            decoration: BoxDecoration(
              color: AppTheme.roundIconBgColor,
              shape: BoxShape.circle,
            ),
            padding: const EdgeInsets.all(6),
            child: Image.asset(
              '$iconAssetpath/eye.png',
              height: iconSize,
              width: iconSize,
            ),
          ),
        ),
      ),
    );
  }
}
