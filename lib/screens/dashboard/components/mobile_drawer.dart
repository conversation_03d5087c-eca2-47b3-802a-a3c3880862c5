import 'package:flutter/material.dart';
import 'package:neorevv/screens/agent/agents_screen.dart';
import 'package:neorevv/screens/dashboard/dashboard_screen.dart';
import 'package:neorevv/screens/sales/sales_table_screen.dart';
import '../../../config/app_strings.dart';
import '../../../enum/user_role.dart';
import '../../../theme/app_fonts.dart';
import '../../../models/user.dart';
import '../../../theme/app_theme.dart';
import '../../../config/constants.dart';

class MobileDrawer extends StatelessWidget {
  final User user;
  final ValueNotifier<String> selectedTab;

  const MobileDrawer({
    super.key,
    required this.user,
    required this.selectedTab,
  });

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Container(
        color: Colors.white,
        child: Column(
          children: [
            // Header section with logo and profile
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: defaultPadding,
                vertical: defaultPadding * 2,
              ),
              decoration: BoxDecoration(color: AppTheme.primaryBlueColor),
              child: Safe<PERSON>rea(
                child: Column(
                  children: [
                    // Logo
                    Image.asset(
                      '$launcherAssetpath/logo.png',
                      scale: 160 / 55,
                      color: Colors.white,
                    ),
                    const SizedBox(height: defaultPadding * 2),

                    // Profile info
                    Row(
                      children: [
                        CircleAvatar(
                          backgroundImage: AssetImage(user.image),
                          radius: 20,
                        ),
                        const SizedBox(width: defaultPadding),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                user.name,
                                style: AppFonts.semiBoldTextStyle(
                                  16,
                                  color: Colors.white,
                                ),
                              ),
                              Text(
                                userRoleToString(user.role),
                                style: AppFonts.regularTextStyle(
                                  12,
                                  color: Colors.white70,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Navigation items
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(vertical: defaultPadding),
                children: [
                  _buildDrawerNavItem(
                    context,
                    dashboardTab,
                    Icons.dashboard_outlined,
                    isSelected: selectedTab.value == dashboardTab,
                  ),
                  _buildDrawerNavItem(
                    context,
                    brokersTab,
                    Icons.business_outlined,
                    isSelected: selectedTab.value == brokersTab,
                  ),
                  _buildDrawerNavItem(
                    context,
                    agentsTab,
                    Icons.people_outline,
                    isSelected: selectedTab.value == agentsTab,
                  ),
                  _buildDrawerNavItem(
                    context,
                    salesTab,
                    Icons.trending_up_outlined,
                    isSelected: selectedTab.value == salesTab,
                  ),
                  _buildDrawerNavItem(
                    context,
                    commissionTab,
                    Icons.account_balance_wallet_outlined,
                    isSelected: selectedTab.value == commissionTab,
                  ),
                  if (user.role != UserRole.agent)
                    _buildDrawerNavItem(
                      context,
                      reportsTab,
                      Icons.assessment_outlined,
                      isSelected: selectedTab.value == reportsTab,
                    ),

                  const Divider(height: defaultPadding * 2),

                  // Action buttons section
                  if (user.role != UserRole.platformOwner)
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: defaultPadding,
                      ),
                      child: ElevatedButton.icon(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: defaultPadding,
                            vertical: defaultPadding,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onPressed: () {
                          Navigator.pop(context);
                          // Handle add new action
                        },
                        icon: const Icon(Icons.add),
                        label: const Text(addNewButton),
                      ),
                    ),

                  const SizedBox(height: defaultPadding),

                  // Settings and notifications
                  _buildDrawerActionItem(
                    context,
                    'Notifications',
                    Icons.notifications_outlined,
                    onTap: () {
                      Navigator.pop(context);
                      // Handle notifications
                    },
                  ),
                  _buildDrawerActionItem(
                    context,
                    'Settings',
                    Icons.settings_outlined,
                    onTap: () {
                      Navigator.pop(context);
                      // Handle settings
                    },
                  ),
                ],
              ),
            ),

            // Footer
            Container(
              padding: const EdgeInsets.all(defaultPadding),
              child: Text(
                'NeoRevv Dashboard',
                style: AppFonts.regularTextStyle(12, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerNavItem(
    BuildContext context,
    String title,
    IconData icon, {
    bool isSelected = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? AppTheme.primaryColor : Colors.grey[600],
      ),
      title: Text(
        title,
        style: isSelected
            ? AppFonts.semiBoldTextStyle(14, color: AppTheme.primaryColor)
            : AppFonts.regularTextStyle(14, color: Colors.black87),
      ),
      selected: isSelected,
      selectedTileColor: AppTheme.primaryColor.withValues(alpha: 0.1),
      onTap: () {
        selectedTab.value = title;
        Navigator.pop(context);

        // Handle navigation
        if (title == dashboardTab) {
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => DashboardScreen()),
            (Route<dynamic> route) => false,
          );
        } else if (title == brokersTab) {
          // Navigate to brokers
        } else if (title == agentsTab) {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => AgentsScreen()),
          );
        } else if (title == salesTab) {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => SalesTableScreen()),
          );
        } else if (title == commissionTab) {
          // Navigate to commission
        } else if (title == reportsTab) {
          // Navigate to reports
        }
      },
    );
  }

  Widget _buildDrawerActionItem(
    BuildContext context,
    String title,
    IconData icon, {
    VoidCallback? onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: Colors.grey[600]),
      title: Text(
        title,
        style: AppFonts.regularTextStyle(14, color: Colors.black87),
      ),
      onTap: onTap,
    );
  }
}
