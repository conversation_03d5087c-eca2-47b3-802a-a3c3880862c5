import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import '../../../theme/app_fonts.dart';
import '../../../theme/app_theme.dart';
import '/config/constants.dart';
import '/config/responsive.dart';

class InfoCardWidget extends StatelessWidget {
  final String title;
  final String value;
  final String icon;
  final Color iconColor;
  final String subtitle;
  final String additionalInfo;

  const InfoCardWidget({
    Key? key,
    required this.title,
    required this.value,
    required this.icon,
    required this.iconColor,
    required this.subtitle,
    required this.additionalInfo,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isMobile = Responsive.isMobile(context);
    final bool isDesktop = Responsive.isDesktop(context);
    final double iconSize = isMobile || Responsive.isTablet(context) ? 45 : 55;
    return Container(
      padding: const EdgeInsets.only(top: defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title section
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: defaultPadding),

            child: Text(
              title,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: AppFonts.mediumTextStyle(
                isDesktop ? 16 : 14,
                color: AppTheme.primaryTextColor,
              ),
            ),
          ),

          // Value and icon section
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  AutoSizeText(
                    value,
                    style: TextStyle(
                      fontSize: isMobile
                          ? 25
                          : !isDesktop
                          ? 30
                          : 35,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                  Container(
                    constraints: BoxConstraints(
                      maxWidth: iconSize,
                      maxHeight: iconSize,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(30),
                      image: DecorationImage(image: AssetImage(icon)),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Footer section
          Container(
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(color: AppTheme.borderColor, width: 1),
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: defaultMargin),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  '$iconAssetpath/calendar.png', // Update this path to your actual calendar icon asset
                  width: 16,
                  height: 16,
                  color: AppTheme.primaryTextColor,
                ),
                const SizedBox(width: 5),
                Expanded(
                  child: AutoSizeText(
                    subtitle,
                    minFontSize: 14,
                    maxLines: 1,
                    style: const TextStyle(
                      color: AppTheme.primaryTextColor,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const Spacer(),

                Container(
                  padding: const EdgeInsets.only(top: 10, bottom: 10, left: 16),
                  //left border
                  decoration: const BoxDecoration(
                    border: Border(
                      left: BorderSide(color: AppTheme.borderColor, width: 1),
                    ),
                  ),
                  child: Row(
                    children: [
                      Image.asset(
                        '$iconAssetpath/user.png', // Update this path to your actual calendar icon asset
                        width: 16,
                        height: 16,
                        color: AppTheme.primaryTextColor,
                      ),
                      const SizedBox(width: 5),
                      Text(
                        additionalInfo,
                        style: const TextStyle(
                          color: AppTheme.primaryTextColor,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to convert string icon name to IconData
  IconData _getIconData() {
    // This is a simplified version - in a real app, you might want to map string names to actual IconData
    // For the example in the image, we're using a user icon
    return Icons.people;
  }
}
